/* eslint-disable max-lines */
import { type LinkingOptions } from '@react-navigation/native';
import ArchivesIndex from '@src/biz/disinfection/archives';
import { DisinfectionCheckout } from '@src/biz/disinfection/checkout';
import GalleryIndex from '@src/biz/disinfection/gallery';
import JurisdictionIndex from '@src/biz/disinfection/jurisdiction';
import ReportFilter from '@src/biz/disinfection/reportFilter';
import SignatureScreen from '@src/biz/disinfection/signature';
import { DisinfectionSignIn } from '@src/biz/disinfection/signIn';
import SituationDescriptionIndex from '@src/biz/disinfection/situationDescription';
import ReportRectification from '@src/biz/disinfection/task/components/ReportRectification';
import TodoRectified from '@src/biz/disinfection/task/components/TodoRectified';
import DisinfectionTaskDetail from '@src/biz/disinfection/task/detail';
import ReportDetail from '@src/biz/disinfection/task/reportDetail';
import { SecondAuditSignIn } from '@src/biz/secondAudit/signIn';
import { SecondAuditSignout } from '@src/biz/secondAudit/signout';
import DifferencesWaitingConfirmIndex from '@src/biz/shop/differencesWaitingConfirm';
import DifferencesWaitingConfirmDetail from '@src/biz/shop/differencesWaitingConfirm/detail';
import ERPEntry from '@src/biz/shop/erpEntry';
import TaskGallery from '@src/biz/shop/gallery';
import FoodSafeArchivesScreen from '@src/biz/shop/home/<USER>';
import DisinfectionReportScreen from '@src/biz/shop/home/<USER>/disinfectionReport';
import FoodSafetyHandbookScreen from '@src/biz/shop/home/<USER>/foodSafetyHandbook';
import OilRecordDetailSheetScreen from '@src/biz/shop/home/<USER>/foodSafetyHandbook/oilRecordDetailSheet';
import OilRecordListScreen from '@src/biz/shop/home/<USER>/foodSafetyHandbook/oilRecordList';
import FoodSafeArchivesStrategyIndex from '@src/biz/shop/home/<USER>';
import FoodSafetyHandbookStrategyIndex from '@src/biz/shop/home/<USER>/foodSafetyHandbookStrategy';
import IceRecordDetailStrategy from '@src/biz/shop/home/<USER>/foodSafetyHandbookStrategy/IceRecordDetailStrategy';
import IceRecordListStrategyIndex from '@src/biz/shop/home/<USER>/foodSafetyHandbookStrategy/IceRecordListStrategyIndex';
import OilRecordDetailSheetStrategyIndex from '@src/biz/shop/home/<USER>/foodSafetyHandbookStrategy/oilRecordDetailSheetStrategy';
import OilRecordListStrategyIndex from '@src/biz/shop/home/<USER>/foodSafetyHandbookStrategy/oilRecordListStrategy';
import LicenseScreen from '@src/biz/shop/license';
import LicenseExample from '@src/biz/shop/license/example';
import PhotoScreen from '@src/biz/shop/photos';
import StrategySelfTask from '@src/biz/shop/strategySelfTask';
import StrategySelfMiddle from '@src/biz/shop/strategySelfTask/StrategySelfMiddle';
import SelfTaskDetailScreen from '@src/biz/shop/task/selfTaskDetail';
import ShopTaskCenterScreen from '@src/biz/shop/task/taskCenter';
import HistorySelfTask from '@src/biz/shop/task/taskCenter/historySelfTask';
import TaskDetailScreen from '@src/biz/shop/task/taskDetail';
import TaskExternalScreen from '@src/biz/shop/task/taskExtend';
import TaskFeedback from '@src/biz/shop/task/taskFeedback';
import TaskRectificationScreen from '@src/biz/shop/task/taskRectification';
import TaskRemark from '@src/biz/shop/task/taskRemark';
import TaskDetailSubmitScreen from '@src/biz/shop/task/taskResubmit';
import SupervisorCheckout from '@src/biz/supervisor/checkout';
import CreateDisinfection from '@src/biz/supervisor/disinfection';
import { EmployeeFaces } from '@src/biz/supervisor/employeeFace';
import EmployeeFaceDetail from '@src/biz/supervisor/employeeFace/detail';
import FoodSafetyComplaintIndex from '@src/biz/supervisor/foodSafetyComplaint';
import { FoodSafetyComplaintDetail } from '@src/biz/supervisor/foodSafetyComplaint/detail';
import FoodSafetyComplaintReject from '@src/biz/supervisor/foodSafetyComplaint/detail/reject';
import FoodSafetyInspectionIndex from '@src/biz/supervisor/foodSafetyInspection';
import SupervisorGalleryIndex from '@src/biz/supervisor/gallery';
import DifferenceAuditThirdStrategyIndex from '@src/biz/supervisor/home/<USER>/DifferenceAuditThirdStrategy';
import DifferenceAuditThirdDetailStrategyIndex from '@src/biz/supervisor/home/<USER>/DifferenceAuditThirdStrategy/detail';
import DifferenceList from '@src/biz/supervisor/home/<USER>/DifferenceList';
import DifferenceDetail from '@src/biz/supervisor/home/<USER>/DifferenceList/differenceDetail';
import DifferenceListStrategy from '@src/biz/supervisor/home/<USER>/DifferenceList-Strategy';
import DifferenceAuditDetailStrategy from '@src/biz/supervisor/home/<USER>/DifferenceList-Strategy/differenceDetail';
import { DifferenceListStrategyHub } from '@src/biz/supervisor/home/<USER>/DifferenceList-Strategy/DifferenceListStrategyHub';
import ExecuteConfirmList from '@src/biz/supervisor/home/<USER>/executeConfirm-Strategy';
import ExecuteTimeSelect from '@src/biz/supervisor/home/<USER>/executeConfirm-Strategy/executeTimeConfirmDetail';
import ApplyDetail from '@src/biz/supervisor/home/<USER>/transferApplyList-Strategy/applyDetail';
import TransferApplyList from '@src/biz/supervisor/home/<USER>/transferApplyList-Strategy/index';
import OptimalPathIndex from '@src/biz/supervisor/optimalPath';
import SupervisorOptions from '@src/biz/supervisor/options';
import SupervisorPlanScreen from '@src/biz/supervisor/planManage';
import PlanSelectionSheet from '@src/biz/supervisor/planSelectionSheet';
import RectificationTaskScreen from '@src/biz/supervisor/rectificationTask';
import ReportApplicationIndex from '@src/biz/supervisor/reportApplication';
import DisinfectionAllotFirm from '@src/biz/supervisor/schedule/allotFirm';
import CrossCheckIndex from '@src/biz/supervisor/schedule/crossCheck';
import DiagnoseWeekReportScreen from '@src/biz/supervisor/schedule/diagnoseWeekReport';
import DiagnosisDetailScreen from '@src/biz/supervisor/schedule/diagnoseWeekReport/diagnosisDetail';
import TacticRectifyDetail from '@src/biz/supervisor/schedule/diagnoseWeekReport/diagnosisDetail/tacticRectify';
import HistoryDiagnosisWeeklyScreen from '@src/biz/supervisor/schedule/diagnoseWeekReport/historyDiagnosisWeekly';
import EmergencySituationScreen from '@src/biz/supervisor/schedule/emergencySituation';
import FoodSafeEditApply from '@src/biz/supervisor/schedule/foodSafeEditApply';
import FoodSafeApplyDetail from '@src/biz/supervisor/schedule/foodSafeEditApply/detail';
import TransferApplyForScreen from '@src/biz/supervisor/schedule/transfer/applyFor';
import TransferApplyDetail from '@src/biz/supervisor/schedule/transfer/applyFor/detail';
import TransferPersonnelSelectScreen from '@src/biz/supervisor/schedule/transfer/personnelSelect';
import SupervisorSignIn from '@src/biz/supervisor/signIn';
import SupervisorStrategyTask from '@src/biz/supervisor/strategyTask';
import CreateTask from '@src/biz/supervisor/task';
import InspectionScreen from '@src/biz/supervisor/taskCenter';
import { switchUserRole } from '@src/hooks';
import { shopPermission, supervisorPermission } from '@src/permission';
import ChangePasswordScreen from '@src/screens/ChangePassword';
import FaceScreen from '@src/screens/Face';
import { FaceManageScreen } from '@src/screens/Face/manage';
import Gallery from '@src/screens/Gallery';
import JumpPatorlTaskHubIndex from '@src/screens/JumpPatorlTaskHub';
import JumpSelfReviewHubIndex from '@src/screens/JumpSelfReviewHub';
import JumpSelfTaskHubIndex from '@src/screens/JumpSelfTaskHub';
import JumpTabHubIndex from '@src/screens/JumpTabHub';
import LibraryGallery from '@src/screens/LibraryGallery';
import LibraryShopList from '@src/screens/LibraryShopList';
import MapSelectionScreen from '@src/screens/Map';
import NoticeScreen from '@src/screens/Notice';
import NoticeDetailScreen from '@src/screens/Notice/detail';
import OrganizationScreen from '@src/screens/Organization';
import { ProblemFeedbackScreen } from '@src/screens/ProblemFeedback';
import ReportCenterScreen from '@src/screens/ReportCenter';
import ShopListScreen from '@src/screens/ShopList';
import { SelectedShopScreen } from '@src/screens/ShopSelected';
import TaskSituationDescription from '@src/screens/SituationDescription';
import StrategyReportItem from '@src/screens/StrategyReportItem';
import StrategyTaskDetail from '@src/screens/StrategyTaskDetail';
import Appeal from '@src/screens/StrategyTaskDetail/Appeal';
import StrategyTaskReview from '@src/screens/StrategyTaskReview';
import TaskCenterScreen from '@src/screens/TaskCenter';
import SelfTaskMiddle from '@src/screens/TaskComponents/SelfTaskMiddle';
import TaskDetailStrategy from '@src/screens/TaskDetailStrategy';
import TaskMiddle from '@src/screens/TaskDetailStrategy/TaskMiddle';
import TaskFeedbackScreen from '@src/screens/TaskFeedback';
import VideoScreen from '@src/screens/Video';
import { useAuth, usePushStore } from '@src/store';
import Toast from '@src/ui/components/Toast';
import { Linking, NativeModules } from 'react-native';
import { URL } from 'react-native-url-polyfill';

export const ShopRouterTable = {
  Task: 'Task',
  // 任务中心
  ShopTaskCenter: 'ShopTaskCenter',
  // 历史自检任务
  HistorySelfTask: 'HistorySelfTask',
  // 整改任务
  TaskRectification: 'TaskRectification',
  // 任务详情
  TaskDetail: 'TaskDetail',
  // 任务详情
  SelfTaskDetail: 'SelfTaskDetail',
  // 任务详情-提交
  TaskDetailSubmit: 'TaskDetailSubmit',
  // 识别提示
  TaskExternal: 'TaskExternal',
  // 任务数据
  TaskData: 'TaskData',
  // 任务报告
  TaskReport: 'TaskReport',
  // 任务报告详情
  TaskReportDetail: 'TaskReportDetail',
  // 门店自检整改任务
  TaskRectSelfCheck: 'TaskRectSelfCheck',
  // 到店巡检整改任务
  TaskRectOnSite: 'TaskRectOnSite',
  // 视频巡检整改任务
  TaskRectVideo: 'TaskRectVideo',
  // AI巡检整改任务
  TaskRectAI: 'TaskRectAI',
  // 整改跟踪
  TaskRectTrack: 'TaskRectTrack',
  // 整改反馈
  TaskRectFeedback: 'TaskRectFeedback',
  License: 'License',
  // 图库管理列表
  LibraryShopList: 'LibraryShopList',
  // 图库照片管理
  LibraryGallery: 'LibraryGallery',
  // 整改反馈
  TaskFeedback: 'TaskFeedback',
  // AI识别错误
  AIIdentifyError: 'AIIdentifyError',
  // ERP小程序首页
  ERPEntry: 'ERPEntry',
  // 食安档案
  FoodSafeArchives: 'FoodSafeArchives',
  // 食安档案-策略
  FoodSafeArchivesStrategy: 'FoodSafeArchivesStrategy',
  // 食安手册
  FoodSafetyHandbook: 'FoodSafetyHandbook',
  // 食安手册-策略
  FoodSafetyHandbookStrategy: 'FoodSafetyHandbookStrategy',
  // 消杀报告
  DisinfectionReport: 'DisinfectionReport',
  // 测/换油记录列表
  OilRecordList: 'OilRecordList',
  // 测/换油记录列表-策略
  OilRecordListStrategy: 'OilRecordListStrategy',
  // 制冰机消毒记录表-策略
  IceRecordListStrategy: 'IceRecordListStrategy',
  // 制冰机消毒记录详情表-策略
  IceRecordDetailStrategy: 'IceRecordDetailStrategy',
  // 换油记录详情表
  OilRecordDetailSheet: 'OilRecordDetailSheet',
  // 换油记录详情表-策略
  OilRecordDetailSheetStrategy: 'OilRecordDetailSheetStrategy',
  // 任务详情-策略
  SelfTaskDetailStrategy: 'SelfTaskDetailStrategy',
  // 任务执行修改申请
  WorkExecuteEditApply: 'WorkExecuteEditApply',
  // 任务执行修改申请详情
  WorkExecuteEditApplyDetail: 'WorkExecuteEditApplyDetail',
  // 任务图库
  TaskGallery: 'TaskGallery',
  // 自检任务中间页
  SelfTaskMiddle: 'SelfTaskMiddle',
  // 自检任务-策略
  StrategySelfTask: 'StrategySelfTask',
  // 自检任务-策略-中间页
  StrategySelfMiddle: 'StrategySelfMiddle',
  // 差异项待确认
  DifferencesWaitingConfirm: 'DifferencesWaitingConfirm',
};

export const SecondSupplierRouterTable = {
  // 签到
  SecondSupplierSignIn: 'SecondSupplierSignIn',
  // 签离
  SecondSupplierSignOut: 'SecondSupplierSignOut',
};

export const SupervisorRouterTable = {
  // 督导-巡检计划列表
  OnSiteTaskPlanList: 'OnSiteTaskPlanList',
  // 督导-计划过滤条件
  PlanSelectionSheet: 'PlanSelectionSheet',
  // 自检报告详情
  SelfCheckReportDetail: 'SelfCheckReportDetail',
  // 自检报告点评
  SelfCheckReportComment: 'SelfCheckReportComment',
  // 自检报告审核
  SelfCheckReportApprove: 'SelfCheckReportApprove',
  // 巡检报告详情
  OnSiteReportDetail: 'OnSiteReportDetail',
  // 巡检报告点评
  OnSiteReportComment: 'OnSiteReportComment',
  // 巡检报告审核
  OnSiteReportApprove: 'OnSiteReportApprove',
  // 巡检任务计划类别
  OnSiteTaskPlan: 'OnSiteTaskPlan',
  // 巡检任务计划编辑
  OnSiteTaskPlanEdit: 'OnSiteTaskPlanEdit',
  // 巡检任务-计划外任务
  OnSiteTaskPlanOut: 'OnSiteTaskPlanOut',
  // 巡检任务-计划外任务添加
  OnSiteTaskPlanOutCreate: 'OnSiteTaskPlanOutCreate',
  // 巡检任务-计划外任务检查表
  OnSiteTaskPlanCheckList: 'OnSiteTaskPlanCheckList',
  //  督导端-结构消杀 任务详情
  OnSiteDisinfectionTaskDetail: 'OnSiteDisinfectionTaskDetail',
  // 紧急消杀情况说明
  EmergencySituation: 'EmergencySituation',
  // 结构消杀 消杀报告过滤
  DisinfectionReportFilter: 'DisinfectionReportFilter',
  // 诊断周报
  DiagnoseWeekReport: 'DiagnoseWeekReport',
  // 诊断明细
  DiagnosisDetail: 'DiagnosisDetail',
  // 历史诊断周报
  HistoryDiagnosisWeekly: 'HistoryDiagnosisWeekly',
  // 签到
  SupervisorSignIn: 'SupervisorSignIn',
  // 签离
  SupervisorCheckout: 'SupervisorCheckout',
  // 任务照片
  SupervisorGallery: 'SupervisorGallery',
  // 转办申请
  TransferApplyFor: 'TransferApplyFor',
  // 转办申请详情
  TransferApplyForDetail: 'TransferApplyForDetail',
  // 转办人员选择
  TransferPersonnelSelect: 'TransferPersonnelSelect',
  // 创建紧急消杀
  CreateDisinfection: 'CreateDisinfection',
  // 创建任务
  CreateTask: 'CreateTask',
  // 中间页面
  SupervisorOptions: 'SupervisorOptions',
  //  人脸审批
  FaceApproval: 'FaceApproval',
  // 人脸审批详情
  FaceApprovalDetail: 'FaceApprovalDetail',
  // 任务详情-策略
  PatrolTaskDetailStrategy: 'PatrolTaskDetailStrategy',
  // 任务详情-情况说明
  TaskSituationDescription: 'TaskSituationDescription',
  // 到店巡检
  TaskCenterNormal: 'TaskCenterNormal',
  // 整改任务列表
  RectificationTask: 'RectificationTask',
  // 申诉待处理任务
  CrossCheck: 'CrossCheck',
  // 任务执行时段确认明细
  ExecuteConfirm: 'ExecuteConfirm',
  // 任务执行时段确认选择
  ExecuteTimeSelect: 'ExecuteTimeSelect',
  // 策略转办申请列表
  StrategyTransferApplyList: 'StrategyTransferApplyList',
  // 策略转办申请详情
  StrategyTransferApplyDetail: 'StrategyTransferApplyDetail',
  // 巡检任务-策略
  SupervisorStrategyTask: 'SupervisorStrategyTask',
  DifferenceAudit: 'DifferenceAudit',
  DifferenceAuditDetail: 'DifferenceAuditDetail',
  // 差异项审核-策略
  DifferenceAuditStrategy: 'DifferenceAuditStrategy',
  // 差异项审核详情-策略
  DifferenceAuditDetailStrategy: 'DifferenceAuditDetailStrategy',
  // 差异项审核详情-策略-中间页
  DifferenceListStrategyHub: 'DifferenceListStrategyHub',
  // 差异项三方审核-策略
  DifferenceAuditThirdStrategy: 'DifferenceAuditThirdStrategy',
  // 食安申诉
  FoodSafetyComplaint: 'FoodSafetyComplaint',
  // 食安申诉详情
  FoodSafetyComplaintDetail: 'FoodSafetyComplaintDetail',
  // 食安申诉驳回
  FoodSafetyComplaintReject: 'FoodSafetyComplaintReject',
  // 最优路径
  OptimalPathIndex: 'OptimalPathIndex',
  // 稽核任务报备申请
  ReportApplicationIndex: 'ReportApplicationIndex',
  // 食安稽核报备处理任务
  FoodSafetyInspection: 'FoodSafetyInspection',
};

export const DisinfectionRouterTable = {
  // 签名页面
  Signature: 'Signature',
  // 签离
  DisinfectionCheckOut: 'DisinfectionCheckOut',
  // 签到
  DisinfectionSignIn: 'DisinfectionSignIn',

  // 结构消杀 分配消杀公司
  AllotDisinfectionFirm: 'AllotDisinfectionFirm',
  // 管辖区域
  Jurisdiction: 'Jurisdiction',
  // 消杀公司档案
  Archives: 'Archives',
  // 不合格情况说明
  SituationDescription: 'SituationDescription',
  // 问题整改
  RectificationFeedBack: 'RectificationFeedBack',
  // 在线审核驳回
  OnlineReject: 'OnlineReject',
  // 到店审核驳回
  OnShopReject: 'OnShopReject',
  // 到店审核通过
  OnShopPass: 'OnShopPass',
  // 报告整改跟踪
  ReportRectification: 'ReportRectification',
  // 报告详情
  ReportDetail: 'ReportDetail',
  // 任务照片
  Gallery: 'Gallery',
};

export const CommonRouterTable = {
  // 任务照片
  PhotosScreen: 'PhotosScreen',
  // 任务图库
  CommonGallery: 'CommonGallery',
  //  修改密码
  ChangePassword: 'ChangePassword',
  //  人脸管理
  FaceManage: 'FaceManage',
  // 人脸识别
  Face: 'Face',
  // 证照页面
  Certification: 'Certification',
  // 证照示例
  CertificationExample: 'CertificationExample',
  // 通知
  Notice: 'Notice',
  // 通知详细
  NoticeDetail: 'NoticeDetail',
  ProblemFeedback: 'ProblemFeedback',
  // 组织架构
  Organization: 'Organization',
  // 地图选择
  MapSelection: 'MapSelection',
  // 选择门店
  ShopSelection: 'ShopSelection',
  // 视频监控选择门店
  ShopSelected: 'ShopSelected',
  // 视频监控详情
  VideoScreen: 'VideoScreen',
  // 切换角色
  RoleScreen: 'RoleScreen',
  // 任务中心
  TaskCenterScreen: 'TaskCenterScreen',
  // 报告中心
  ReportCenter: 'ReportCenter',
  // 整改反馈
  TaskFeedbackScreen: 'TaskFeedbackScreen',
  // 任务中间页
  TaskMiddle: 'TaskMiddle',
  //
  DiagnoseWeekReportScreen: 'DiagnoseWeekReportScreen',
  // 备注
  TaskRemark: 'TaskRemark',
  // 结构消杀 任务详情
  DisinfectionTaskDetail: 'DisinfectionTaskDetail',
  // 结构消杀 分配消杀公司
  OnSiteAllotDisinfectionFirm: 'OnSiteAllotDisinfectionFirm',
  // 跳转tab中间页
  JumpTabHub: 'JumpTabHub',
  // 策略任务详情
  StrategyTaskDetail: 'StrategyTaskDetail',
  // 策略申诉
  Appeal: 'Appeal',
  // 策略申诉-理由
  AppealReason: 'AppealReason',
  // 策略任务详情
  StrategyTaskReview: 'StrategyTaskReview',
  // 策略任务点评中间页
  JumpSelfReviewHub: 'JumpSelfReviewHub',
  // 策略整改详情
  TacticRectifyDetail: 'TacticRectifyDetail',
  // 差异项三方审核详情-策略
  DifferenceAuditThirdDetailStrategy: 'DifferenceAuditThirdDetailStrategy',
  // 差异项到店确认详情页
  DifferencesWaitingConfirmDetail: 'DifferencesWaitingConfirmDetail',
  // 巡检任务中间页
  JumpPatorlTaskHub: 'JumpPatorlTaskHub',
  // 自检任务中间页
  JumpSelfTaskHub: 'JumpSelfTaskHub',
  // 提报项
  StrategyReportItem: 'StrategyReportItem',
};

export const SecondSupplierRoute: TstOm.StackParams.RouterProps[] = [
  {
    name: SecondSupplierRouterTable.SecondSupplierSignIn,
    component: SecondAuditSignIn,
    options: {
      title: '签到',
    },
  },
  {
    name: SecondSupplierRouterTable.SecondSupplierSignOut,
    component: SecondAuditSignout,
    options: {
      title: '签离',
    },
  },
];

export const DisinfectionRoute: TstOm.StackParams.RouterProps[] = [
  {
    name: DisinfectionRouterTable.Signature,
    component: SignatureScreen,
    options: {
      title: '电子签名',
    },
  },
  {
    name: DisinfectionRouterTable.DisinfectionCheckOut,
    component: DisinfectionCheckout,
    options: {
      title: '签离',
    },
  },
  {
    name: DisinfectionRouterTable.DisinfectionSignIn,
    component: DisinfectionSignIn,
    options: {
      title: '签到',
    },
  },
  {
    name: DisinfectionRouterTable.AllotDisinfectionFirm,
    component: DisinfectionAllotFirm,

    options: {
      title: '分配消杀公司',
      headerShown: true,
    },
  },
  {
    name: DisinfectionRouterTable.Jurisdiction,
    component: JurisdictionIndex,
    options: {
      title: '管辖区域',
    },
  },
  {
    name: DisinfectionRouterTable.Archives,
    component: ArchivesIndex,
    options: {
      title: '消杀公司档案',
    },
  },
  {
    name: DisinfectionRouterTable.SituationDescription,
    component: SituationDescriptionIndex,
    options: {
      title: '情况说明',
    },
  },
  {
    name: DisinfectionRouterTable.RectificationFeedBack,
    // @ts-ignore
    component: TodoRectified,

    options: {
      title: '问题整改追踪',
      headerShown: true,
    },
  },
  {
    name: DisinfectionRouterTable.OnlineReject,
    component: TaskFeedback,

    options: {
      title: '在线审核驳回',
      headerShown: true,
    },
  },
  {
    name: DisinfectionRouterTable.OnShopReject,
    component: TaskFeedback,

    options: {
      title: '到店审核驳回',
      headerShown: true,
    },
  },
  {
    name: DisinfectionRouterTable.OnShopPass,
    component: TaskFeedback,

    options: {
      title: '到店审核通过',
      headerShown: true,
    },
  },
  {
    name: DisinfectionRouterTable.ReportRectification,
    component: ReportRectification,

    options: {
      title: '整改跟踪',
      headerShown: true,
    },
  },
  {
    name: DisinfectionRouterTable.Gallery,
    component: GalleryIndex,
    options: {
      title: '任务照片',
    },
  },
];

export const CommonRoute: TstOm.StackParams.RouterProps[] = [
  {
    name: CommonRouterTable.Certification,
    component: LicenseScreen,
    options: {
      title: '证照',
      headerShown: true,
    },
  },
  {
    name: DisinfectionRouterTable.ReportDetail,
    component: ReportDetail,
    options: {
      title: '报告详情',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.CertificationExample,
    component: LicenseExample,
    options: {
      title: '示例图片',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.CommonGallery,
    component: Gallery,
    options: {
      title: '任务图库',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.PhotosScreen,
    component: PhotoScreen,
    options: {
      title: '任务照片',
    },
  },
  {
    name: CommonRouterTable.ChangePassword,
    component: ChangePasswordScreen,

    options: {
      headerShown: true,
      title: '',
      headerTransparent: true,
    },
  },
  {
    name: CommonRouterTable.Notice,
    component: NoticeScreen,
    options: {
      title: '通知',
    },
  },
  {
    name: CommonRouterTable.NoticeDetail,
    component: NoticeDetailScreen,
    options: {
      title: '通知详情',
    },
  },
  {
    name: CommonRouterTable.FaceManage,
    component: FaceManageScreen,
    options: {
      title: '人脸管理',
    },
  },
  {
    name: CommonRouterTable.ProblemFeedback,
    component: ProblemFeedbackScreen,
    options: {
      title: '问题反馈',
    },
  },
  {
    name: CommonRouterTable.Organization,
    component: OrganizationScreen,
    options: {
      title: '组织架构',
    },
  },
  {
    name: CommonRouterTable.MapSelection,
    component: MapSelectionScreen,
    options: {
      headerShown: false,
    },
  },
  {
    name: CommonRouterTable.ShopSelection,
    component: ShopListScreen,
    options: {
      title: '选择门店',
    },
  },
  {
    name: CommonRouterTable.ShopSelected,
    component: SelectedShopScreen,
    permission: [supervisorPermission.MyPermissionEnum.视频监控, shopPermission.HomePermissionEnum.视频监控],
    options: {
      title: '选择门店',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.VideoScreen,
    component: VideoScreen,
    permission: [supervisorPermission.MyPermissionEnum.视频监控, shopPermission.HomePermissionEnum.视频监控],
    options: {
      title: '视频巡检',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.Face,
    component: FaceScreen,

    options: {
      title: '人脸识别',
      headerShown: true,
    },
  },
  // {
  //   name: CommonRouterTable.RoleScreen,
  //   component: RoleScreen,
  //
  //   options: {
  //     title: '角色选择',
  //   },
  // },
  {
    name: CommonRouterTable.TaskCenterScreen,
    component: TaskCenterScreen,
    options: {
      title: '任务中心',
    },
  },
  {
    name: CommonRouterTable.ReportCenter,
    component: ReportCenterScreen,

    options: {
      title: '报告中心',
    },
  },
  {
    name: CommonRouterTable.TaskFeedbackScreen,
    component: TaskFeedbackScreen,

    options: {
      title: '整改反馈',
    },
  },
  {
    name: CommonRouterTable.TaskMiddle,
    component: TaskMiddle,

    options: {
      title: '',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.TaskRemark,
    component: TaskRemark,
    options: {
      title: '添加备注',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.DisinfectionTaskDetail,
    component: DisinfectionTaskDetail,

    options: {
      title: '任务详情',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.OnSiteAllotDisinfectionFirm,
    component: DisinfectionAllotFirm,

    options: {
      title: '分配消杀公司',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.JumpTabHub,
    component: JumpTabHubIndex,
    options: {
      title: '跳转中',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.StrategyTaskDetail,
    component: StrategyTaskDetail,

    options: {
      title: '任务详情',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.Appeal,
    component: Appeal,
    options: {
      title: '申诉',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.StrategyTaskReview,
    component: StrategyTaskReview,
    options: {
      title: '任务点评',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.JumpSelfReviewHub,
    component: JumpSelfReviewHubIndex,
    options: {
      title: '跳转中',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.TacticRectifyDetail,
    component: TacticRectifyDetail,
    options: {
      title: '整改详情',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.DifferenceAuditThirdDetailStrategy,
    component: DifferenceAuditThirdDetailStrategyIndex,
    options: {
      title: '差异项三方审核详情',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.DifferencesWaitingConfirmDetail,
    component: DifferencesWaitingConfirmDetail,
    options: {
      title: '差异项到店确认详情',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.JumpPatorlTaskHub,
    component: JumpPatorlTaskHubIndex,
    options: {
      title: '跳转中',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.JumpSelfTaskHub,
    component: JumpSelfTaskHubIndex,
    options: {
      title: '跳转中',
      headerShown: true,
    },
  },
  {
    name: CommonRouterTable.StrategyReportItem,
    component: StrategyReportItem,
    options: {
      title: '提报项',
      headerShown: true,
    },
  },
];

export const OMShopRoute: TstOm.StackParams.RouterProps[] = [
  {
    name: ShopRouterTable.ShopTaskCenter,
    component: ShopTaskCenterScreen,

    options: {
      title: '任务中心',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.TaskDetail,
    component: TaskDetailScreen,

    options: {
      title: '任务详情',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.SelfTaskDetail,
    component: SelfTaskDetailScreen,
    options: {
      title: '自检任务详情',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.SelfTaskMiddle,
    component: SelfTaskMiddle,
    options: {
      title: '检查表',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.HistorySelfTask,
    component: HistorySelfTask,

    options: {
      title: '历史自检任务',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.TaskDetailSubmit,
    component: TaskDetailSubmitScreen,

    options: {
      title: '检查表',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.TaskExternal,
    component: TaskExternalScreen,

    options: {
      title: '提示',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.TaskRectification,
    component: TaskRectificationScreen,

    options: {
      title: '整改任务',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.License,
    component: LicenseScreen,
    permission: [shopPermission.MyPermissionEnum.证照管理],
    options: {
      title: '证照管理',
    },
  },
  {
    name: ShopRouterTable.LibraryShopList,
    component: LibraryShopList,
    options: {
      title: '图库管理',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.LibraryGallery,
    component: LibraryGallery,

    options: {
      title: '图库管理',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.TaskFeedback,
    component: TaskFeedback,

    options: {
      title: '整改反馈',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.AIIdentifyError,
    component: TaskFeedback,

    options: {
      title: 'AI识别错误',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.ERPEntry,
    component: ERPEntry,

    options: {
      title: '货物管理',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.FoodSafeArchives,
    component: FoodSafeArchivesScreen,

    options: {
      title: '食安档案',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.FoodSafeArchivesStrategy,
    component: FoodSafeArchivesStrategyIndex,

    options: {
      title: '食安档案-策略',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.DisinfectionReport,
    component: DisinfectionReportScreen,
    options: {
      title: '消杀报告',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.FoodSafetyHandbook,
    component: FoodSafetyHandbookScreen,
    options: {
      title: '食安手册',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.FoodSafetyHandbookStrategy,
    component: FoodSafetyHandbookStrategyIndex,
    options: {
      title: '食安手册-策略',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.OilRecordList,
    component: OilRecordListScreen,
    options: {
      title: '测油/换油记录表',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.OilRecordListStrategy,
    component: OilRecordListStrategyIndex,
    options: {
      title: '测油/换油记录表-策略',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.OilRecordDetailSheet,
    component: OilRecordDetailSheetScreen,
    options: {
      title: '换油记录表',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.OilRecordDetailSheetStrategy,
    component: OilRecordDetailSheetStrategyIndex,
    options: {
      title: '换油记录表-策略',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.IceRecordListStrategy,
    component: IceRecordListStrategyIndex,
    options: {
      title: '制冰机消毒记录表-策略',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.IceRecordDetailStrategy,
    component: IceRecordDetailStrategy,
    options: {
      title: '制冰机消毒详情-策略',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.SelfTaskDetailStrategy,
    component: TaskDetailStrategy,
    options: {
      title: '任务详情-策略',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.WorkExecuteEditApply,
    component: FoodSafeEditApply,
    options: {
      title: '任务执行修改申请',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.WorkExecuteEditApplyDetail,
    component: FoodSafeApplyDetail,
    options: {
      title: '任务执行修改申请详情',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.TaskGallery,
    component: TaskGallery,
    options: {
      title: '任务图库',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.StrategySelfTask,
    component: StrategySelfTask,
    options: {
      title: '自检任务-策略',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.StrategySelfMiddle,
    component: StrategySelfMiddle,
    options: {
      title: '检查表',
      headerShown: true,
    },
  },
  {
    name: ShopRouterTable.DifferencesWaitingConfirm,
    component: DifferencesWaitingConfirmIndex,
    options: {
      title: '差异项待确认',
      headerShown: true,
    },
  },
];

export const OMSupervisorRoute: TstOm.StackParams.RouterProps[] = [
  {
    name: SupervisorRouterTable.OnSiteTaskPlanList,
    component: SupervisorPlanScreen,

    options: {
      title: '计划管理',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.PlanSelectionSheet,
    component: PlanSelectionSheet,

    options: {
      title: '选择',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.OnSiteDisinfectionTaskDetail,
    component: DisinfectionTaskDetail,

    options: {
      title: '任务详情',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.EmergencySituation,
    component: EmergencySituationScreen,

    options: {
      title: '紧急消杀任务详情',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.DisinfectionReportFilter,
    component: ReportFilter,

    options: {
      title: '选择',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.DiagnoseWeekReport,
    component: DiagnoseWeekReportScreen,

    options: {
      title: '诊断周报',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.DiagnosisDetail,
    component: DiagnosisDetailScreen,

    options: {
      title: '诊断明细',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.HistoryDiagnosisWeekly,
    component: HistoryDiagnosisWeeklyScreen,

    options: {
      title: '历史诊断周报',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.SupervisorSignIn,
    component: SupervisorSignIn,

    options: {
      title: '签到',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.SupervisorCheckout,
    component: SupervisorCheckout,

    options: {
      title: '签离',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.SupervisorGallery,
    component: SupervisorGalleryIndex,
    options: {
      title: '任务照片',
    },
  },
  {
    name: SupervisorRouterTable.TransferApplyFor,
    component: TransferApplyForScreen,

    options: {
      title: '任务转办申请',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.TransferApplyForDetail,
    component: TransferApplyDetail,

    options: {
      title: '转办申请详情',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.TransferPersonnelSelect,
    component: TransferPersonnelSelectScreen,

    options: {
      title: '转办人员选择',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.CreateDisinfection,
    component: CreateDisinfection,

    options: {
      title: '发起紧急消杀任务',
    },
  },
  {
    name: SupervisorRouterTable.SupervisorOptions,
    component: SupervisorOptions,
    options: {
      title: '',
    },
  },
  {
    name: SupervisorRouterTable.FaceApproval,
    component: EmployeeFaces,

    options: {
      title: '员工修改人脸审批',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.CreateTask,
    component: CreateTask,
    options: {
      title: '',
    },
  },
  {
    name: SupervisorRouterTable.FaceApprovalDetail,
    component: EmployeeFaceDetail,

    options: {
      title: '员工修改人脸审批',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.PatrolTaskDetailStrategy,
    component: TaskDetailStrategy,

    options: {
      title: '任务详情-策略',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.SupervisorStrategyTask,
    component: SupervisorStrategyTask,
    options: {
      title: '巡检任务-策略',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.TaskSituationDescription,
    component: TaskSituationDescription,

    options: {
      title: '任务详情-策略',
      headerShown: true,
    },
  },

  {
    name: SupervisorRouterTable.TaskCenterNormal,
    // @ts-ignore
    component: InspectionScreen,

    options: {
      title: '任务中心',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.RectificationTask,
    component: RectificationTaskScreen,
    options: {
      title: '整改任务',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.CrossCheck,
    component: CrossCheckIndex,
    options: {
      title: '申诉待处理任务',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.ExecuteConfirm,
    component: ExecuteConfirmList,
    options: {
      title: '任务执行时段确认明细',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.ExecuteTimeSelect,
    component: ExecuteTimeSelect,
    options: {
      title: '任务执行时段确认',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.StrategyTransferApplyList,
    component: TransferApplyList,
    options: {
      title: '转办任务申请列表',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.StrategyTransferApplyDetail,
    component: ApplyDetail,
    options: {
      title: '转办任务申请详情',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.DifferenceAudit,
    component: DifferenceList,
    options: {
      title: '差异项审核列表',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.DifferenceAuditDetail,
    component: DifferenceDetail,
    options: {
      title: '差异项审核详情',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.DifferenceAuditStrategy,
    component: DifferenceListStrategy,
    options: {
      title: '差异项审核列表（新）',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.DifferenceAuditDetailStrategy,
    component: DifferenceAuditDetailStrategy,
    options: {
      title: '差异项审核详情',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.DifferenceListStrategyHub,
    component: DifferenceListStrategyHub,
    options: {
      title: '跳转中',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.DifferenceAuditThirdStrategy,
    component: DifferenceAuditThirdStrategyIndex,
    options: {
      title: '差异项三方审核',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.FoodSafetyComplaint,
    component: FoodSafetyComplaintIndex,
    options: {
      title: '申诉列表',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.FoodSafetyComplaintDetail,
    component: FoodSafetyComplaintDetail,
    options: {
      title: '申诉详情',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.FoodSafetyComplaintReject,
    component: FoodSafetyComplaintReject,
    options: {
      title: '驳回',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.OptimalPathIndex,
    component: OptimalPathIndex,
    options: {
      title: '最优路径',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.ReportApplicationIndex,
    component: ReportApplicationIndex,
    options: {
      title: '稽核任务报备申请',
      headerShown: true,
    },
  },
  {
    name: SupervisorRouterTable.FoodSafetyInspection,
    component: FoodSafetyInspectionIndex,
    options: {
      title: '食安稽核报备处理任务',
      headerShown: true,
    },
  },
];

const { ExternalEventModule } = NativeModules;

export const OMLinking: LinkingOptions<any> = {
  prefixes: ['om-fundation://'],
  config: {
    initialRouteName: 'Main',
    screens: {
      WebView: 'webview',
      TaskDetail: 'taskdetail/:id/:workSheetId',
      DisinfectionTaskDetail: 'disinfectionTaskDetail/:id/:orderType/:taskId',
      OnSiteDisinfectionTaskDetail: 'OnSiteDisinfectionTaskDetail/:id/:orderType/:taskId',
      JumpTabHub: 'JumpTabHub',
      StrategyTransferApplyList: 'StrategyTransferApplyList',
      SelfTaskDetailStrategy: 'SelfTaskDetailStrategy',
      RectificationTask: 'RectificationTask',
      DiagnoseWeekReport: 'DiagnoseWeekReport',
      JumpSelfReviewHub: 'JumpSelfReviewHub',
      PatrolTaskDetailStrategy: 'PatrolTaskDetailStrategy',
      DifferenceAuditThirdDetailStrategy: 'DifferenceAuditThirdDetailStrategy',
      DifferencesWaitingConfirmDetail: 'DifferencesWaitingConfirmDetail',
      DifferenceAuditThirdDetail: 'DifferenceAuditThirdDetail',
      DifferenceListStrategyHub: 'DifferenceListStrategyHub',
      JumpPatorlTaskHub: 'JumpPatorlTaskHub',
      EmergencySituation: 'EmergencySituation',
      CreateDisinfection: 'CreateDisinfection',
      StrategyTaskDetail: 'StrategyTaskDetail',
      RectificationFeedBack: 'RectificationFeedBack',
      FoodSafetyComplaint: 'FoodSafetyComplaint',
      ReportApplicationIndex: 'ReportApplicationIndex',
      FoodSafetyInspection: 'FoodSafetyInspection',
    },
  },
  subscribe: (listener) => {
    const onReceiveURL = ({ url }: any) => {
      const params = new URL(url);
      const mobile = params.searchParams.get('mobile');
      const { user } = useAuth.getState();
      const roleType = params.searchParams.get('roletype');
      const appJump = params?.searchParams.get('mode');
      const lubanUrl = params?.searchParams.get('lubanurl');
      const onlyPullUpApp = params?.searchParams.get('onlyPullUpApp');

      if (onlyPullUpApp) {
        return;
      }

      if (appJump === 'luban') {
        // 未登录不执行后续跳转动作
        if (useAuth.getState().loginStatus !== 'login') {
          useAuth.persist.clearStorage();
          usePushStore.persist.clearStorage();
          useAuth.getState().signOut();

          return;
        }

        const roleList = useAuth.getState().currentRoleList?.filter((item) => item.roleCategory === '2');

        if (!roleList?.length) {
          Toast.warn({
            message: '暂无权限',
          });

          return;
        }

        if (roleList && roleList?.length > 0) {
          useAuth.getState().setCurrentRole(roleList[0]);
          switchUserRole(roleList[0]);
        }

        ExternalEventModule.emitEventToNative('om_fundation', 'route', {
          routerName: lubanUrl,
        });
      } else if (user?.mobile !== mobile || useAuth.getState().loginStatus !== 'login') {
        useAuth.persist.clearStorage();
        usePushStore.persist.clearStorage();
        useAuth.getState().signOut();
      } else {
        const roleList = useAuth.getState().currentRoleList?.filter((item) => item.roleCategory === roleType);

        if (roleList && roleList?.length > 0) {
          useAuth.getState().setCurrentRole(roleList[0]);
          switchUserRole(roleList[0]);
        }

        listener(url);
      }
    };

    const subscribeLinking = Linking.addEventListener('url', onReceiveURL);

    return () => subscribeLinking.remove();
  },
  getInitialURL: () => Linking.getInitialURL(),
};
