import { useCallback, useEffect, useId, useRef, useState } from 'react';
import { useFocusEffect, useIsFocused, useNavigation, useRoute } from '@react-navigation/native';
import AddApply from '@src/assets/icons/addApply.svg';
import { PopupRef } from '@src/biz/disinfection/task/components/Popup';
import TstOm from '@src/components';
import { Button, LinearGradient, showToast } from '@src/components/Components';
import { usePermission } from '@src/hooks';
import { report } from '@src/http/report';
import { useQueryPatrolPlanCount } from '@src/http/service/supervisorTask';
import { useMutationTransferTask } from '@src/http/service/transfer';
import { supervisorPermission } from '@src/permission';
import { SchedulePermissionEnum } from '@src/permission/supervisor';
import { useGray } from '@src/store';
import Permission from '@src/ui/components/Permission';
import Tabs from '@src/ui/components/Tabs';
import { isNil } from 'lodash';
import { ImageBackground, SafeAreaView, StatusBar, Text, TouchableOpacity, View } from 'react-native';
import { scheduleEventEmitter } from './components/TaskItem';
import { TodayTask } from './components/TodayTask';
import { WaitTodoTask } from './components/WaitTodoTask';
import useTaskCountTotal from './hooks/useTaskCountTotal';
import { ShopConfigProvider } from './shopConfigContext';
import TaskTransferReason from './transfer/personnelSelect/components/TaskTransferReason';

enum TabsEnum {
  今日任务 = 0,
  待办任务 = 1,
  '今日任务 (新)' = 2,
  '待办任务 (新)' = 3,
}

const SupervisorScheduleScreen = () => {
  const isGrayUser = useGray((s) => s.isGrayUser);
  const { params } = useRoute<any>() as {
    params: {
      ScheduleTabsV2: TabsEnum;
    };
  };

  const [activeTabKey, setActiveTabKey] = useState<TabsEnum>(
    isGrayUser ? TabsEnum['今日任务 (新)'] : TabsEnum.今日任务,
  );

  // 是否为转办操作
  const [isTransfer, setIsTransfer] = useState<boolean>(false);
  const { checkPartPermission, checkPermission } = usePermission();
  const navigation = useNavigation<TstOm.StackParams.ScreenNavigationProp['navigation']>();
  const popupRef = useRef<PopupRef>(null);
  const isFocused = useIsFocused();

  // 允许转办
  const [allowTransfer] = useState<boolean>(false);
  // 是否显示转办操作按钮
  const isShowTransfer = allowTransfer && activeTabKey === 1;

  useEffect(() => {
    if (!isNil(params?.ScheduleTabsV2)) {
      setActiveTabKey(+params?.ScheduleTabsV2);
      // 更新完, 清空params
      navigation.setParams({
        ScheduleTabsV2: undefined,
      });
    }
  }, [navigation, params?.ScheduleTabsV2]);

  const { mutateAsync: createTransferTask } = useMutationTransferTask({
    onSuccess: () => {
      showToast({ title: '创建成功', icon: 'none' });

      setTimeout(() => {
        popupRef.current?.onClose();
      }, 100);
    },
    onError: (error) => {
      console.log('error :>> ', error);

      throw new Error(`创建转办任务失败: ${error}`);
    },
    onSettled: () => {
      setTimeout(() => {
        popupRef.current?.onClose();
      }, 150);
    },
  });

  useFocusEffect(
    useCallback(() => {
      activeTabKey === 1 && setIsTransfer(false);
    }, [activeTabKey]),
  );

  const menuList = [
    {
      id: useId(),
      title: '任务中心',
      image: <TstOm.Icon name="task" className="fill-[#FFFFFF]" />,
      // permission: checkPartPermission(['dataBoard:tasks']),
      permission: checkPartPermission([supervisorPermission.SchedulePermissionEnum.任务中心]),
      onClick: () => {
        report({
          type: 'ability',
          page: '督导日程',
          abilityButton: '任务中心',
        });
        navigation.navigate('WebView', { url: '/tasks/list' });
      },
    },
    {
      id: useId(),
      title: '任务中心 (新)',
      image: <TstOm.Icon name="task" className="fill-[#FFFFFF]" />,
      permission: isGrayUser && checkPartPermission([supervisorPermission.SchedulePermissionEnum['任务中心(新)']]),
      onClick: () => {
        report({
          type: 'ability',
          page: '督导日程',
          abilityButton: '到店巡检',
        });
        navigation.navigate('TaskCenterNormal');
      },
    },
    {
      id: useId(),
      title: '计划管理',
      image: <TstOm.Icon name="plan-manage" className="fill-[#FFFFFF]" />,
      showCount: true,
      // permission: checkPartPermission(['$app$planManagement']),
      permission: checkPartPermission([supervisorPermission.SchedulePermissionEnum.计划管理]),
      onClick: () => {
        report({
          type: 'ability',
          page: '督导日程',
          abilityButton: '计划管理',
        });
        navigation.navigate('OnSiteTaskPlanList');
      },
    },
    {
      id: useId(),
      title: '报告中心',
      image: <TstOm.Icon name="report-center" className="fill-[#FFFFFF]" />,
      permission: checkPartPermission(supervisorPermission.reportEntrancePermissions),
      onClick: () => {
        report({
          type: 'ability',
          page: '督导日程',
          abilityButton: '报告中心',
        });
        navigation.navigate('WebView', { url: '/mission?roletype=supervision' });
      },
    },
    {
      id: useId(),
      title: '报告中心 (新)',
      image: <TstOm.Icon name="report-center" className="fill-[#FFFFFF]" />,
      permission: isGrayUser && checkPermission(supervisorPermission.SchedulePermissionEnum['报告中心(新)']),
      onClick: () => {
        report({
          type: 'ability',
          page: '督导日程',
          abilityButton: '报告中心 (新)',
        });
        navigation.navigate('ReportCenter');
      },
    },
    {
      id: useId(),
      title: '申诉报告',
      image: <TstOm.Icon name="Complaint" className="fill-[#FFFFFF]" />,
      permission: checkPermission(supervisorPermission.SchedulePermissionEnum['食安申诉报告']),
      onClick: () => {
        navigation.navigate('FoodSafetyComplaint');
      },
    },
  ];

  const { data: patrolPlanCount = 0 as any, refetch: patrolPlanCountFecth } = useQueryPatrolPlanCount({
    queryKey: ['queryPatrolPlanCount'],
    params: {
      planTypeList: ['NORMAL', 'FOOD_SAFETY_NORMAL'],
    },
  });

  const {
    dealTaskCountSum,
    tacticsTodayTaskCountSum,
    tacticsTodoTaskCountSum,
    taskCountSumFecth,
    todayTaskCountTacticsRefetch,
    todoTaskCountTacticsRefetch,
  } = useTaskCountTotal({ isFocused });

  const tacticsCountRefetch = useCallback(() => {
    const RefetchMap = {
      [TabsEnum['今日任务 (新)']]: todayTaskCountTacticsRefetch,
      [TabsEnum['待办任务 (新)']]: todoTaskCountTacticsRefetch,
    };

    return RefetchMap[activeTabKey as keyof typeof RefetchMap]?.();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTabKey]);

  useFocusEffect(
    useCallback(() => {
      taskCountSumFecth();
      tacticsCountRefetch();
      patrolPlanCountFecth();
    }, [patrolPlanCountFecth, tacticsCountRefetch, taskCountSumFecth]),
  );

  useEffect(() => {
    scheduleEventEmitter.addListener('REACT_NATIVE_SCHEDULE_TASK_REFRESH', (msg) => {
      if (msg?.isGrayUser) {
        tacticsCountRefetch();
      } else {
        taskCountSumFecth();
        patrolPlanCountFecth();
      }
    });

    return () => {
      scheduleEventEmitter.removeAllListeners('REACT_NATIVE_SCHEDULE_TASK_REFRESH');
    };
  }, [patrolPlanCountFecth, tacticsCountRefetch, taskCountSumFecth]);

  /*  useEffect(() => {
    setActiveTabKey(0);
  }, [params]); */

  const currentTaskEnable = checkPermission(SchedulePermissionEnum['今日任务(新)']);
  const currentTodoTaskEnable = checkPermission(SchedulePermissionEnum['待办任务(新)']);

  return (
    <ImageBackground
      className="flex-1"
      style={{
        paddingTop: StatusBar.currentHeight ?? 0,
      }}
      source={require('@assets/images/bg.png')}
    >
      <ShopConfigProvider>
        <SafeAreaView className="mx-3 mb-5 flex-1">
          <View className="h-12 items-center justify-center">
            <Text className="text-lg font-medium text-base-black-2">任务</Text>
          </View>
          {/* 顶部 Card */}
          <View className="my-2 flex-row flex-wrap gap-y-4 rounded-[12px] bg-white py-5">
            {menuList
              .filter((m) => m.permission)
              .map((item) => (
                <TouchableOpacity
                  className="w-1/4 items-center gap-y-2"
                  key={item.id}
                  activeOpacity={0.8}
                  onPress={() => item.onClick?.()}
                >
                  <View className="size-12">{item.image}</View>
                  <Text className="text-sm text-[#5E5E5E]">{item.title}</Text>
                  {item.showCount && !!patrolPlanCount && (
                    <View className="absolute right-4.5 top-0 rounded-full bg-[#CD3E36] px-1">
                      <Text className="ml-px text-xs font-normal text-white">
                        {patrolPlanCount > 99 ? '99+' : patrolPlanCount}
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              ))}
          </View>
          <Tabs
            textClassName="text-[14px]"
            textActiveClassName="text-[14px]"
            indicatorWidth={50}
            tabAlign="left"
            activeKey={`${activeTabKey}`}
            onChange={(activeKey) => setActiveTabKey(+activeKey)}
          >
            {isGrayUser && currentTaskEnable && currentTodoTaskEnable && (
              <>
                {currentTaskEnable && (
                  <Tabs.TabPane key="2" tab={`今日待完成任务 (${tacticsTodayTaskCountSum.dayTaskCountSum || 0})`}>
                    {activeTabKey === TabsEnum['今日任务 (新)'] && (
                      <TodayTask isGrayUser taskCountSum={tacticsTodayTaskCountSum as any} popupRef={popupRef} />
                    )}
                  </Tabs.TabPane>
                )}
                {currentTodoTaskEnable && (
                  <Tabs.TabPane key="3" tab={`待完成任务 (总) (${tacticsTodoTaskCountSum.dayTaskCountSum || 0})`}>
                    {activeTabKey === TabsEnum['待办任务 (新)'] && (
                      <WaitTodoTask isGrayUser taskCountSum={tacticsTodoTaskCountSum as any} popupRef={popupRef} />
                    )}
                  </Tabs.TabPane>
                )}
              </>
            )}
            <Tabs.TabPane key="0" tab={`今日任务 (${dealTaskCountSum?.dayTaskCountSum || 0})`}>
              {activeTabKey === TabsEnum.今日任务 && <TodayTask taskCountSum={dealTaskCountSum} popupRef={popupRef} />}
            </Tabs.TabPane>
            <Tabs.TabPane key="1" tab={`待办任务 (${dealTaskCountSum?.unfinishedTaskCount || 0})`}>
              {activeTabKey === TabsEnum.待办任务 && (
                <WaitTodoTask taskCountSum={dealTaskCountSum} popupRef={popupRef} />
              )}
            </Tabs.TabPane>
            {/* {activeTabKey === 1 && (
              <WeekTask
                popupRef={popupRef}
                isTransfer={isTransfer}
                isShowTransfer={isShowTransfer}
                setIsTransfer={setIsTransfer}
                setAllowTransfer={setAllowTransfer}
              />
            )} */}
          </Tabs>
        </SafeAreaView>
      </ShopConfigProvider>
      {isTransfer && isShowTransfer ? null : (
        <View className="relative flex-row items-center gap-x-3 p-4">
          {/* {checkPartPermission(['dataBoard:tasks:create:crop']) && ( */}
          <Permission permission={supervisorPermission.SchedulePermissionEnum.创建任务}>
            <Button
              className="flex-1 border-primary"
              variant="outline"
              onPress={() => {
                navigation.navigate('CreateTask');
              }}
            >
              <Text className="text-center text-base font-medium text-primary">创建任务</Text>
            </Button>
          </Permission>
          {isShowTransfer && (
            <LinearGradient
              className=" flex-row items-center justify-center rounded-full p-3.5"
              colors={['#7990FF', '#0928BB']}
              start={{ x: 0, y: 0 }}
              end={{ x: 0, y: 1 }}
            >
              <TouchableOpacity
                className="items-center justify-center gap-y-2 rounded-full"
                activeOpacity={0.8}
                onPress={() => {
                  setIsTransfer(true);
                }}
              >
                <AddApply width={20} height={20} fill="#ffffff" />
                <Text className="text-xs font-normal text-white">申请转办</Text>
              </TouchableOpacity>
            </LinearGradient>
          )}
        </View>
      )}
      <TaskTransferReason
        popupRef={popupRef}
        onConfirm={async (reason, initValue) => {
          if (!reason?.length || !initValue?.taskId) {
            return;
          }

          await createTransferTask({
            taskId: initValue?.taskId,
            newInspector: initValue?.newInspector,
            reason,
          });
        }}
      />
    </ImageBackground>
  );
};

export default SupervisorScheduleScreen;
