import { useEffect, useMemo, useRef, useState } from 'react';
import { useNavigation, useRoute } from '@react-navigation/native';
import { PopupRef } from '@src/biz/disinfection/task/components/Popup';
import { useTabs } from '@src/biz/supervisor/home/<USER>';
import { showModal } from '@src/components/Dialog';
import { PopupSheetRef } from '@src/components/Popup/PopupSheet';
import { usePermission } from '@src/hooks';
import { usePatrolTransferUserList } from '@src/http/service/transfer';
import { SchedulePermissionEnum } from '@src/permission/supervisor';
import { Icon, Tabs } from '@src/ui/components/components';
import { isNil } from 'lodash';
import { Text, View } from 'react-native';
import { tacticsTodoTaskList } from '../../../home/<USER>';
import PersonnelFilter from '../../transfer/personnelSelect/components/PersonnelFilter';
import Sort, { SortEnum } from '../Sort';
import { WaitTaskItem } from '../WaitTaskItem';

interface TProps {
  taskCountSum: TstOm.SuervisorTaskCountSumService.Result;
  popupRef: React.RefObject<PopupRef>;
  /** 是否为灰度用户 */
  isGrayUser?: boolean;
}

export function WaitTodoTask({ taskCountSum, popupRef, isGrayUser }: TProps) {
  const { checkPartPermission } = usePermission();
  const [activeTabKey, setActiveTabKey] = useState<number>(0);
  const [taskId, setTaskId] = useState<number>();
  const [queryType, setQueryType] = useState<string>('');
  const [orderType, setOrderType] = useState<SortEnum>(SortEnum.ASC);
  const { params } = useRoute<any>();
  const navigation = useNavigation<TstOm.StackParams.ScreenNavigationProp['navigation']>();

  const personPopupSheetRef = useRef<PopupSheetRef>(null);
  const { waitTodoTaskTypes } = useTabs();
  const permissionTabs = useMemo(() => waitTodoTaskTypes.filter((item) => item.permission), [waitTodoTaskTypes]);
  // 策略待办任务列表
  const tacticsPermissionTabs = useMemo(() => (isGrayUser ? tacticsTodoTaskList : []), [isGrayUser]);

  const displayTabs = useMemo(() => {
    return (isGrayUser ? tacticsPermissionTabs : permissionTabs).map((item) => {
      if (item.queryType === 'ALL') {
        return '全部';
      } else {
        // @ts-ignore
        const count = taskCountSum[item.field] > 99 ? '99+' : taskCountSum[item.field] ?? 0;

        return `${item.title} (${count})`;
      }
    });
  }, [isGrayUser, taskCountSum, tacticsPermissionTabs, permissionTabs]);

  const { data: transferUserList } = usePatrolTransferUserList({
    enabled: true,
  });

  const displayTabsContent = (isGrayUser ? tacticsPermissionTabs : permissionTabs)?.map((item) => {
    return item.queryType === queryType ? (
      <WaitTaskItem
        key={`taskItem-${item.queryType}`}
        orderType={orderType}
        status={item.queryType}
        isGrayUser={isGrayUser}
        onHandTransfer={(id: number, isNewInspector?: boolean) => {
          if (isNewInspector) {
            setTaskId(id);
            personPopupSheetRef.current?.onOpen();

            return;
          }

          popupRef.current?.onOpen({ taskId: id });
        }}
      />
    ) : null;
  });

  useEffect(() => {
    if (!isNil(params?.queryType) || !isNil(params?.message?.queryType)) {
      let type = params?.queryType || params?.message?.queryType;
      let filterData = (isGrayUser ? tacticsPermissionTabs : permissionTabs).find((m) => m.queryType === type);

      if (!filterData) {
        filterData = isGrayUser ? tacticsPermissionTabs[0] : permissionTabs[0];
        type = filterData?.queryType;
      }

      const tabIndex = displayTabs.findIndex((n) => n.includes(filterData.title));

      setActiveTabKey(tabIndex);
      setQueryType(type);

      // 更新完, 清空params
      navigation.setParams({
        queryType: undefined,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params]);

  const onTabChange = (index: number) => {
    const filterData = (isGrayUser ? tacticsPermissionTabs : permissionTabs)[index];

    setActiveTabKey(index);
    setQueryType(filterData?.queryType);
  };

  return (
    <>
      <Tabs
        textClassName="text-[14px]"
        textActiveClassName="text-[14px]"
        indicatorWidth={30}
        tabAlign="left"
        activeKey={`${activeTabKey}`}
        onChange={(v) => {
          onTabChange(+v);
          setOrderType(SortEnum.ASC);
        }}
      >
        {displayTabs?.map((m, idx) => {
          // 待办任务(新)_点评任务|巡检任务
          const permitInspect = idx === 0 && checkPartPermission([SchedulePermissionEnum['待办任务(新)_巡检任务']]);
          const permitInterview = idx === 1 && checkPartPermission([SchedulePermissionEnum['待办任务(新)_点评任务']]);
          const permitFoodSafety = idx === 2 && checkPartPermission([SchedulePermissionEnum['待办任务(新)_稽核计划']]);

          // 策略的待办任务(新)增加了独立权限点，与待办任务的需要分开控制
          if (isGrayUser) {
            return permitInspect || permitInterview || permitFoodSafety ? (
              <Tabs.TabPane key={`${idx}`} tab={m}>
                <View className="flex flex-1">
                  {isGrayUser && idx !== 2 && (
                    <View className="my-2 flex flex-row justify-end">
                      <Sort text="时间排序" value={orderType} onChange={setOrderType} />
                    </View>
                  )}
                  <View className="flex-row items-center gap-x-2 rounded-lg bg-white px-3">
                    <Icon name="searchTask" />
                    <View className="flex flex-1 gap-y-1">
                      <View className="flex-row gap-x-2">
                        <View className="flex-row items-end">
                          <Text className="text-[20px] leading-5 text-[#1D2129]">9</Text>
                          <Text className="text-display-2xs text-[#1D2129]"> / 24</Text>
                        </View>
                        <Text className="text-display-2xs text-[#86909C]">本月任务进度</Text>
                      </View>
                      {/* 进度条 */}
                      <View className="relative h-2 w-full overflow-hidden rounded-[64px] bg-[#F2F3F5]">
                        <View
                          className="h-full rounded-[64px] bg-[#0E42D2]"
                          style={{ width: `${((9 / 24) * 100).toFixed(2)}%` as any }}
                        />
                      </View>
                    </View>
                  </View>
                  {displayTabsContent}
                </View>
              </Tabs.TabPane>
            ) : null;
          } else {
            return (
              <Tabs.TabPane key={`${idx}`} tab={m}>
                <View className="flex flex-1">
                  {isGrayUser && (
                    <View className="my-2 flex flex-row justify-end">
                      <Sort text="时间排序" value={orderType} onChange={setOrderType} />
                    </View>
                  )}
                  {displayTabsContent}
                </View>
              </Tabs.TabPane>
            );
          }
        })}
      </Tabs>
      <PersonnelFilter
        popupSheetRef={personPopupSheetRef}
        userData={transferUserList as any}
        onSelect={(v) => {
          showModal({
            title: '提示',
            content: `确定指派${v?.nickName}？`,
            success: async (res) => {
              if (res.confirm) {
                popupRef.current?.onOpen({ taskId: taskId!, newInspector: v?.userId });
              }
            },
          });
        }}
      />
    </>
  );
}
