import { useNavigation } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import { AppTodoTaskListAuditPlanResponse } from '@src/http/service/tactics/supervisorTask';
import { Items } from '@src/http/service/task-center';
import { Icon } from '@src/ui/components/components';
import dayjs from 'dayjs';
import { Text, TouchableWithoutFeedback, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { FoodSafetyPlanTaskCard } from './FoodSafetyPlanTaskCard';

enum ESign {
  不需要签到,
  需要签到,
  需要签到签离,
}

export const FoodSafetyPlanCard = (props: AppTodoTaskListAuditPlanResponse) => {
  const { items } = props || {};

  const navigation = useNavigation<TstOm.StackParams.ScreenNavigationProp['navigation']>();

  const renderNode = () => {
    if (props?.appStatisticTaskType === 'FOOD_SAFETY_NORMAL_AUDIT_PLAN_TASK') {
      return (
        <View className="overflow-hidden  rounded-lg">
          <LinearGradient className="p-3" colors={['#EBEFFC', '#FFFFFF']}>
            <View className="flex-row justify-between">
              <View className="flex-row items-center gap-x-1">
                <View className="overflow-hidden rounded-[4px] bg-[#6AA1FF] px-[4.5px]">
                  <Text className="text-display-xs text-white">{props?.batchId}</Text>
                </View>
                <Text className="text-display-2sm font-medium text-[#1D2129]">
                  已查门店：{props?.auditShopCountComplete}/{props?.auditShopCount}
                </Text>
              </View>
            </View>
            <Text className="mt-[1px] text-display-2xs text-[#86909C]">
              执行时段：{dayjs(props?.startTime).format('YYYY/MM/DD HH:mm')}-
              {dayjs(props?.expiredTime).format('YYYY/MM/DD HH:mm')}
            </Text>
            <TouchableWithoutFeedback
              onPress={() => {
                navigation.navigate('OptimalPathIndex', {
                  batchId: props?.batchId,
                });
              }}
            >
              <View className="mt-1 h-8 flex-row items-center justify-center gap-x-1 rounded-lg border-[0.5px] border-[#0E42D2] bg-white">
                <Icon name="compass" className="size-4" />
                <Text className="text-display-sm text-[#0E42D2]">查看最优路径</Text>
              </View>
            </TouchableWithoutFeedback>
          </LinearGradient>
          <FlashList<Items>
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            data={items || []}
            renderItem={({ item }) => <FoodSafetyPlanTaskCard {...item} key={item?.id} startTime={props?.startTime} />}
            estimatedItemSize={100}
            keyExtractor={(item) => item?.id?.toString() || ''}
          />
        </View>
      );
    }

    if (props?.appStatisticTaskType === 'FOOD_SAFETY_NORMAL_AUDIT_PLAN_TRANSFER_TASK') {
      return (
        <View className="overflow-hidden  rounded-lg">
          <LinearGradient className="p-3" colors={['#FFECE0', '#FFFFFF']}>
            <View className="flex-row justify-between">
              <Text className="text-display-2sm font-medium text-[#1D2129]">转办任务：{items?.length || 0}</Text>
            </View>
          </LinearGradient>
          <FlashList<Items>
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            data={items || []}
            renderItem={({ item }) => <FoodSafetyPlanTaskCard {...item} key={item?.id} startTime={props?.startTime} />}
            estimatedItemSize={104}
            keyExtractor={(item) => item?.id?.toString() || ''}
          />
        </View>
      );
    }

    return null;
  };

  return renderNode();
};
