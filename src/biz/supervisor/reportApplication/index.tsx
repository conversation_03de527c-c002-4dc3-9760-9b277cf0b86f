import { Button } from '@src/ui/components/Button';
import Fields from '@src/ui/template/form/Fields';
import { FormProvider } from '@src/ui/template/form/provide';
import { cn } from '@src/utils';
import { useForm } from 'react-hook-form';
import { SafeAreaView, ScrollView, Text, View } from 'react-native';

const ISeparator = ({ className }: { className?: string }) => (
  <View className={cn('my-3 h-[0.5px] w-full bg-[#E5E6EB]', className)} />
);

export default function ReportApplicationIndex() {
  const form = useForm({ mode: 'onChange' });

  return (
    <SafeAreaView className="flex-1 p-3">
      <ScrollView className="flex-1">
        <FormProvider value={{ form }}>
          <View className="w-full rounded-lg bg-white p-3">
            <Fields.Checkbox
              label="异常申请原因"
              name="reason"
              checkboxProps={{
                direction: 'horizontal',
                options: [
                  {
                    label: '个人原因',
                    value: 'PERSONAL',
                  },
                  {
                    label: '门店原因',
                    value: 'SHOP',
                  },
                ],
              }}
            />
            <ISeparator />
            <View>
              <Fields.Selector
                label="具体原因"
                name="copyUserIds"
                selectorOption={{ title: '具体原因', options: [{ label: 'aasdfa', value: 1 }], search: true }}
                componentClassName="flex-col gap-y-2"
                placeholder="请选择修改原因"
              />
            </View>
            <ISeparator />
            <Fields.Checkbox
              label="是否需要将任务转派至其他人"
              name="reason2"
              checkboxProps={{
                direction: 'horizontal',
                options: [
                  {
                    label: '是',
                    value: 'yes',
                  },
                  {
                    label: '否',
                    value: 'no',
                  },
                ],
              }}
            />
          </View>
          <View className="mt-3 w-full rounded-lg bg-white p-3">
            <Fields.DateRangePicker
              label="任务执行时间修改"
              rules={{
                required: {
                  value: true,
                  message: '请选择任务执行时间',
                },
              }}
              name="loopEndTime"
              datePicker={{
                title: '任务执行时间',
                mode: 'Y-s',
                min: new Date(),
              }}
              dateFormat="YYYY-MM-DD HH:mm:ss"
            />
          </View>
          <View className="mt-3 w-full rounded-lg bg-white p-3">
            <Fields.Input
              label={'辅助材料'}
              name="remark"
              inputOptions={{
                className: 'h-[8rem] bg-[#F7F8FA] px-3',
                maxLength: 140,
                multiline: true,
                textAlignVertical: 'top',
                placeholder: '请输入详细的情况说明，最多140字',
              }}
              rules={{
                required: {
                  value: true,
                  message: '请填写申诉说明',
                },
              }}
            />
            <Fields.Photos
              name="attachmentIds"
              pickerType={['cameraPhoto', 'cropCameraVideo', 'cropPicker']}
              fileBusinessType="PATROL"
            />
          </View>
        </FormProvider>
      </ScrollView>
      <View className="bg-white px-3 py-2">
        <Button onPress={form.handleSubmit(() => {})}>
          <Text className="text-base font-medium text-white">确定</Text>
        </Button>
      </View>
    </SafeAreaView>
  );
}
