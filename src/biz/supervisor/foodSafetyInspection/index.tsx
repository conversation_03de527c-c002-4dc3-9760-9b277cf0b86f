import { useRoute } from '@react-navigation/native';
import { AppealStatus } from '@src/types/tactics/appeal_enum';
import Search from '@src/ui/template/task/Components/Search';
import { CheckWayCNToEnEnum } from '@src/ui/template/task/interface';
import useSearchOptions from '@src/ui/template/task/useSearchOptions';
import { formatDateToUTC } from '@src/utils';
import dayjs from 'dayjs';
import { View } from 'react-native';
import { InspectionCard } from './components/InspectionCard';

export default function FoodSafetyInspectionIndex() {
  const { params: { status } = {} } = useRoute() as { params: { status: AppealStatus } };

  const { params, onChange, options } = useSearchOptions<any>({
    defaultParams: {
      beginTime: formatDateToUTC(dayjs().subtract(1, 'month').startOf('day').format()),
      endTime: formatDateToUTC(dayjs().endOf('day').format()),
      statuses: status,
    },
  });

  return (
    <View className="flex-1 bg-[#F7F8FA]">
      <Search
        value={{
          time: [dayjs(params?.beginTime).toDate(), dayjs(params?.endTime).toDate()],
          groupId: params?.groupId,
          shopIds: params.shopIds?.length ? params.shopIds.join('') : undefined,
          custom: {
            statuses: params?.statuses,
            taskSubType: params?.taskSubType,
          },
        }}
        options={options.concat([
          {
            type: 'Button',
            divider: true,
            dataIndex: 'custom',
            itemOptions: [
              {
                title: '状态',
                titleKey: 'statuses',
                items: [
                  { label: '全部', value: undefined },
                  { label: '待审核', value: AppealStatus.发起 },
                  { label: '已审核', value: AppealStatus.已提交 },
                  { label: '已作废', value: AppealStatus.已作废 },
                  { label: '已过期', value: AppealStatus.已过期 },
                  { label: '已撤回', value: AppealStatus.撤回 },
                ],
              },
              {
                title: '巡检类型',
                titleKey: 'taskSubType',
                items: [
                  { label: '全部', value: undefined },

                  {
                    label: '食安线下稽核',
                    value: CheckWayCNToEnEnum['食安线下稽核'],
                  },
                  {
                    label: '食安线上稽核',
                    value: CheckWayCNToEnEnum['食安线上稽核'],
                  },
                ],
              },
            ],
          },
        ])}
        onSearchParamsChange={(val) => {
          onChange({
            beginTime: !val?.time[0]
              ? formatDateToUTC(dayjs().startOf('day'))
              : formatDateToUTC(dayjs(val?.time[0]).startOf('day')),
            endTime: !val?.time[1]
              ? formatDateToUTC(dayjs().endOf('day'))
              : formatDateToUTC(dayjs(val?.time[1]).endOf('day')),
            shopIds: !val?.shopIds ? undefined : [val?.shopIds],
            groupId: val?.groupId,
            ...val.custom,
          });
        }}
      />
      <View className="flex-1 p-3">
        <InspectionCard />
      </View>
    </View>
  );
}
