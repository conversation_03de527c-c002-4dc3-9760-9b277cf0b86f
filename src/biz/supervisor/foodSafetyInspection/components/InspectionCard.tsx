import { Text, View } from 'react-native';
import show from './PassPopup';
import { InfoCard, StatusButton } from '../../foodSafetyComplaint/components/ComplaintDescCard';
import { ISeparator } from '../../foodSafetyComplaint/components/ISeparator';

interface InspectionCardProps {}

export const InspectionCard = ({}: InspectionCardProps) => {
  return (
    <>
      <View className="rounded-lg bg-white p-3">
        <View className="flex-row items-center justify-between">
          <Text className="text-display-sm font-medium  text-[#1D2129]">审核截止至2025/08/20 15:24</Text>
          <Text className="text-display-sm font-medium  text-[#0E42D2]">待审核</Text>
        </View>
        <ISeparator />
        <View>
          <Text className="text-display-2sm font-medium  text-[#1D2129]">TW0083 武汉市江夏东湖学院店</Text>
          <View className="flex-row items-center">
            <Text className="text-display-sm font-medium  text-[#1D2129]">申请异常原因：</Text>
            <Text className="rounded-[2px] border-[0.5px] border-[#7BE188] px-1 text-display-2xs text-[#7BE188]">
              个人原因
            </Text>
          </View>
          <Text className="text-display-sm font-medium  text-[#1D2129]">
            具体原因：啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊
          </Text>
          <Text className="text-display-sm font-medium  text-[#1D2129]">
            任务执行时间：啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊
          </Text>
          <Text className="text-display-sm font-medium  text-[#1D2129]">是否可延期执行：是</Text>
          <InfoCard
            remarkLabel="辅助材料"
            remark={'啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊'}
            attachments={[]}
            containerClassName="mt-2"
          />
        </View>
        <ISeparator />
        {/* 操作按钮区域 */}
        <View className=" flex-row gap-x-2">
          <>
            <StatusButton status="REJECTED" label="驳回" currentStatus={undefined} onPress={() => {}} />
            <StatusButton
              status="PASSED"
              label="审核通过"
              currentStatus={undefined}
              onPress={() => {
                show();
              }}
            />
          </>
        </View>
      </View>
    </>
  );
};
