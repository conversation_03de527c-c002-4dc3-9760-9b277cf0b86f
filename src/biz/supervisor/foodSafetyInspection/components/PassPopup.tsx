import { memo, useEffect, useState } from 'react';
import Popup from '@src/ui/components/Popup';
import Portal from '@src/ui/components/Portal';
import Fields from '@src/ui/template/form/Fields';
import { FormProvider } from '@src/ui/template/form/provide';
import { useForm } from 'react-hook-form';
import { Text, TouchableOpacity, View } from 'react-native';

interface PassPopupProps {
  onClose?: () => void;
  // CTODO:
  onSubmit?: (val: any) => void;
}

export const PassPopup = memo(({ onClose, onSubmit }: PassPopupProps) => {
  const [visible, setVisible] = useState(false);
  const form = useForm({ mode: 'onChange' });

  function closeModal() {
    setVisible(false);
    // 先关闭后，在销毁
    setTimeout(() => {
      onClose?.();
    }, 500);
  }

  useEffect(() => {
    setVisible(true);
  }, []);

  function handleRequestPopupClose() {
    closeModal();

    return true;
  }

  return (
    <Popup round destroyOnClosed visible={visible} onPressOverlay={closeModal} onRequestClose={handleRequestPopupClose}>
      <View className="w-full rounded-xl bg-white">
        <View className="px-6">
          <View className="w-full flex-row justify-center">
            <Text className="mt-8 text-display-base font-medium text-[#1D2129]">审核通过确认</Text>
          </View>
          <FormProvider value={{ form }}>
            <Fields.Selector
              label="转办人员"
              name="copyUserIds"
              selectorOption={{
                title: '转办人员',
                options: [{ label: 'aasdfa', value: 1 }],
                search: true,
              }}
              componentClassName="flex-col gap-y-2"
              placeholder="请选择转办人员"
              cellContainerClassName="py-0"
            />
          </FormProvider>
        </View>
        <View className="h-14 flex-row border-t-[0.5px] border-[#E5E6EB]">
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={closeModal}
            className="flex-1 items-center justify-center border-r-[0.5px] border-[#E5E6EB]"
          >
            <Text className="text-[16px] font-medium text-[#5E5E5E]">取消</Text>
          </TouchableOpacity>
          <TouchableOpacity activeOpacity={0.8} className="flex-1 items-center justify-center" onPress={() => {}}>
            <Text className="text-[16px] font-medium text-primary">确定</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Popup>
  );
});

export default function show() {
  // CTODO:
  return new Promise<any | null>((resolve) => {
    const key = Portal.add(
      <PassPopup
        onClose={() => {
          Portal.remove(key);
          resolve(null);
        }}
        onSubmit={(val) => {
          Portal.remove(key);
          resolve(val);
        }}
      />,
    );
  });
}
