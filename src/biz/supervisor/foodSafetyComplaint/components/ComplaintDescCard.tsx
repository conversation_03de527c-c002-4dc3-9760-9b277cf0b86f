import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { Button } from '@src/components/Button';
import { useMutationAppealAdjudication } from '@src/http/service/tactics/appeal';
import CheckItemInfo from '@src/screens/StrategyTaskComponents/Checklist/CheckItem/Info';
import CheckItemResult from '@src/screens/StrategyTaskComponents/Checklist/CheckItem/Result';
import CheckItemSampleImage from '@src/screens/StrategyTaskComponents/Checklist/CheckItem/SimpleImage';
import ItemWrapper from '@src/screens/StrategyTaskComponents/ItemWrapper';
import { Attachment, IAppealDetail } from '@src/types/tactics/appeal';
import AlertDialog from '@src/ui/components/AlertDialog';
import Icon from '@src/ui/components/Icon';
import ImagePreview from '@src/ui/components/Preview/ImagePreview';
import VideoPreview from '@src/ui/components/Preview/VideoPreview';
import Uploader from '@src/ui/components/Uploader';
import { cn } from '@src/utils';
import dayjs from 'dayjs';
import { Text, TouchableOpacity, View } from 'react-native';
import ComplaintRecordPopup, { OprAction } from './ComplaintRecordPopup';
import { ISeparator } from './ISeparator';

// 状态样式配置
const STATUS_STYLES = {
  PASSED: {
    border: 'border-[#0E42D2]',
    background: 'bg-[#E8F3FF]',
    text: 'text-[#0E42D2]',
    label: '判定为合格',
  },
  REJECTED: {
    border: 'border-[#F76560]',
    background: 'bg-[#FFECE8]',
    text: 'text-[#F76560]',
    label: '维持原结果',
  },
  DEFAULT: {
    border: 'border-[#C9CDD4]',
    background: '',
    text: 'text-[#1D2129]',
  },
} as const;

// 基础样式
const BASE_STYLES = {
  container: 'h-8 w-1/2 shrink-1 rounded-lg border-DEFAULT',
  text: 'text-display-sm font-medium leading-[22px]',
} as const;

// 状态按钮组件
interface StatusButtonProps {
  status: 'PASSED' | 'REJECTED';
  currentStatus?: string;
  onPress?: () => void;
  disabled?: boolean;
  label?: string;
}

export const StatusButton = ({ status, currentStatus, onPress, disabled = false, label }: StatusButtonProps) => {
  const isActive = currentStatus === status;
  const styles = STATUS_STYLES[status];
  const defaultStyles = STATUS_STYLES.DEFAULT;

  const containerClass = cn(
    BASE_STYLES.container,
    isActive ? `${styles.border} ${styles.background}` : defaultStyles.border,
  );

  const textClass = cn(BASE_STYLES.text, isActive ? styles.text : defaultStyles.text);

  return (
    <Button
      variant="outline"
      size="sm"
      onPress={disabled || isActive ? undefined : onPress}
      disabled={disabled}
      className={containerClass}
    >
      <Text className={textClass}>{label || styles.label}</Text>
    </Button>
  );
};

export const ComplaintDescCard = ({
  itemInfo,
  remark,
  attachments,
  status,
  isPending,
  id,
  onRefresh,
  taskId,
  baseTaskId,
  auditUserName,
  auditRemark,
  auditAttachmentIds,
  // 申诉审核时间
  updateTime,
  // 申诉发起人
  createUserName,
  // 申诉发起时间
  createTime,
}: IAppealDetail & {
  isPending: boolean;
  onRefresh: () => void;
  taskId: number;
  baseTaskId: number;

  createUserName: string;

  createTime: string;
}) => {
  const [recordPopupProps, setRecordPopupProps] = useState<{ visible: boolean; data?: any }>({
    visible: false,
  });

  const { mutateAsync: appealAdjudicationMutateAsync } = useMutationAppealAdjudication();
  const navigation = useNavigation<TstOm.StackParams.ScreenNavigationProp['navigation']>();

  const passHandle = async () => {
    const res = await AlertDialog.Dialog({
      title: '是否确认审核通过',
    });

    if (res === 'confirm') {
      await appealAdjudicationMutateAsync({
        baseTaskId,
        patrolReportAppealItemId: id,
        remark: '',
        status: 'PASSED',
        taskId,
      });

      setTimeout(() => {
        onRefresh();
      }, 200);
    }
  };
  const rejectHandle = () => {
    navigation.navigate('FoodSafetyComplaintReject', {
      baseTaskId,
      taskId,
      patrolReportAppealItemId: id,
    });
  };

  return (
    <View className="rounded-lg bg-white p-3">
      <ItemWrapper
        key={itemInfo?.worksheetItemId}
        value={JSON.stringify({
          ...itemInfo,
          worksheetItem: itemInfo?.worksheetItemSnap && JSON.parse(itemInfo?.worksheetItemSnap),
        })}
      >
        <CheckItemInfo />
        <CheckItemSampleImage simpleImages={itemInfo?.simpleImages} />
        <CheckItemResult />
      </ItemWrapper>

      <ISeparator />

      {/* 申诉信息区域 */}
      <View className="flex-row gap-x-1">
        <View className="flex size-6 items-center justify-center rounded-full bg-primary">
          <Text className="text-display-sm font-medium leading-[22px] text-white">{createUserName?.charAt(0)}</Text>
        </View>
        <View className="flex-1 gap-y-2">
          <View className="flex-row items-center justify-between">
            <View className="flex-row gap-x-1">
              <Text className="text-display-sm leading-6 text-[#4E5969]">{createUserName}</Text>
              <Text className="text-display-sm leading-6 text-[#4E5969]">发起申诉</Text>
            </View>
            <Text className="text-display-2xs leading-5 text-[#86909C]">
              {createTime ? dayjs(createTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
            </Text>
          </View>
          <InfoCard remark={remark} attachments={attachments} />
        </View>
      </View>

      {/* 进度指示 */}
      {[OprAction.审核驳回, OprAction.审核通过].includes(status as any) && (
        <View className="mx-auto mt-2">
          <TouchableOpacity
            className="flex-row items-center"
            onPress={() => {
              setRecordPopupProps({
                visible: true,
                // 吃饱撑的,在把信息放进度里在展示一遍. 所以自己构造进度数据了.
                data: [
                  {
                    operatorName: createUserName,
                    updateTime: createTime,
                    operateAction: OprAction.发起申诉,
                    extra: <InfoCard remark={remark} attachments={attachments} />,
                  },
                  [OprAction.审核驳回, OprAction.审核通过].includes(status as any)
                    ? {
                        operatorName: auditUserName,
                        updateTime,
                        operateAction: status,
                        extra: OprAction.审核驳回 ? (
                          <InfoCard remark={auditRemark} attachments={auditAttachmentIds} remarkLabel="驳回原因" />
                        ) : null,
                      }
                    : undefined,
                ]?.filter(Boolean),
              });
            }}
          >
            <Text className="text-display-sm leading-[22px] text-[#86909C]">全部进度</Text>
            <Icon name="arrowDown" className="ml-1.5 text-display-2xs text-[#86909C]" />
          </TouchableOpacity>
        </View>
      )}

      {/* 操作按钮区域 */}
      <View className="mt-3 flex-row gap-x-2">
        {isPending ? (
          <>
            <StatusButton status="PASSED" currentStatus={status} onPress={passHandle} />
            <StatusButton status="REJECTED" currentStatus={status} onPress={rejectHandle} />
          </>
        ) : (
          <>
            {(status === 'PASSED' || status === 'REJECTED') && (
              <StatusButton status={status as 'PASSED' | 'REJECTED'} currentStatus={status} disabled />
            )}
          </>
        )}
      </View>
      <ComplaintRecordPopup
        visible={recordPopupProps?.visible}
        data={recordPopupProps?.data}
        onClose={() => {
          setRecordPopupProps({
            visible: false,
          });
        }}
      />
    </View>
  );
};

interface InfoCardProps {
  remark?: string;
  attachments?: Attachment[];
  remarkLabel?: string;
  containerClassName?: string;
}

export const InfoCard = ({ remark, attachments, remarkLabel = '申诉理由', containerClassName }: InfoCardProps) => {
  if (remark || attachments?.length) {
    return (
      <View className={cn('rounded-lg bg-[#F7F8FA] p-2', containerClassName)}>
        {remark && (
          <Text className="text-display-2sm leading-[22px] text-[#4E5969]">
            <Text className="text-display-2sm font-medium leading-[22px] text-[#5B6A91]">{remarkLabel}：</Text>
            {remark}
          </Text>
        )}
        {!!attachments?.length && (
          <Uploader.Preview
            list={attachments?.map((i) => {
              return {
                fileId: i?.id,
                fileUrl: i?.snapshotUrl || i?.url,
                type: i?.contentType?.toLocaleLowerCase().includes('image') ? 'image' : 'video',
                previewPath: i?.url,
                fileName: '',
              };
            })}
            customPreview={{
              image: ImagePreview,
              video: VideoPreview,
            }}
          />
        )}
      </View>
    );
  }

  return null;
};
