import { Cell, Selector as RNSelector } from '@src/ui/components/components';
import { SelectorOptions } from '@src/ui/components/Selector/interface';
import { Controller } from 'react-hook-form';
import { Text } from 'react-native';
import Animated, { Easing, FadeIn } from 'react-native-reanimated';
import { FileProps } from '../instance';
import { useFormContext } from '../provide';

function Selector({
  label,
  selectorOption,
  lefUnit,
  rightUnit,
  ...restProps
}: FileProps & { selectorOption: SelectorOptions; lefUnit?: string; rightUnit?: string } & {
  componentClassName?: string;
  cellContainerClassName?: string;
  placeholder?: string;
  customDescRender?: ({ label, value }: { label: string; value: any }) => React.ReactNode;
}) {
  const { form } = useFormContext();

  return (
    <Controller
      {...restProps}
      control={form.control}
      render={({ field: { onChange, value }, formState: { errors } }) => (
        <>
          <Cell
            size="small"
            title={label}
            titleClassName="text-[14px] font-medium text-base-black-2"
            onClick={() => {
              RNSelector({
                ...selectorOption,
                value,
              })
                .then((val) => {
                  onChange?.(val);
                })
                .catch(() => {});
            }}
            desc={
              restProps?.customDescRender ? (
                restProps?.customDescRender({
                  label: selectorOption.multiple
                    ? (value || [])?.length
                    : selectorOption?.options?.find((item) => item.value === value)?.label || restProps?.placeholder,
                  value,
                })
              ) : (
                <Text className="text-sm text-[#58595B]">
                  {lefUnit}
                  {selectorOption.multiple
                    ? (value || [])?.length
                    : selectorOption?.options?.find((item) => item.value === value)?.label || restProps?.placeholder}
                  {rightUnit}
                </Text>
              )
            }
            componentClassName={restProps?.componentClassName}
            cellContainerClassName={restProps?.cellContainerClassName}
          />
          {typeof errors[restProps.name]?.message === 'string' ? (
            <Animated.Text entering={FadeIn.duration(500).easing(Easing.ease)} className="text-[14px] text-red-500">
              {(errors[restProps.name]?.message as string) ?? ''}
            </Animated.Text>
          ) : null}
        </>
      )}
    />
  );
}

export default Selector;
