import { memo, useCallback, useEffect, useMemo } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import { RevocationEmit } from '@src/hooks';
import http from '@src/http/client';
import { InspectionRequest, InspectionResponse, TaskStatus } from '@src/http/service/task-center';
import { ClientError } from '@src/http/utils';
import { AlarmLevelEnum } from '@src/types/service/diagnosticEnum';
import { Empty, Loading } from '@src/ui/components/components';
import { formatDateToUTC } from '@src/utils';
import { ScreenTTIMeasure } from '@src/utils/sentry';
import { useInfiniteQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { isNil } from 'lodash';
import { Dimensions, RefreshControl, Text, View } from 'react-native';
import InspectionCard from '../Card/inspection';
import Search from '../Components/Search';
import { CheckWayCNToEnEnum, InspectionProps, yytTaskStatusCNToEnEnum } from '../interface';
import useSearchOptions from '../useSearchOptions';

/**
 * 巡检任务
 * @returns
 */
function InspectionPage({ url, isSuperManger }: InspectionProps) {
  const { params, onChange, options } = useSearchOptions<InspectionRequest & { alarmLevel?: string }>({
    defaultParams: {
      startDate: dayjs().subtract(30, 'day').startOf('day'),
      endDate: dayjs().endOf('day'),
      groupId: undefined,
      taskSubType: undefined,
    },
  });

  const { isLoading, isRefetching, isFetching, data, hasNextPage, refetch, fetchNextPage } = useInfiniteQuery<
    InspectionResponse,
    ClientError
  >({
    queryKey: ['InspectionList', isSuperManger, params],
    queryFn: async ({ pageParam = 1 }) => {
      const { taskStatus, ...rest } = params || {};

      const buildTaskStatus = () => {
        if (isNil(taskStatus)) {
          return {};
        }

        // @ts-ignore
        if (taskStatus === -1) {
          return {
            taskStatus: yytTaskStatusCNToEnEnum['已完成'],
            hasExpired: true,
          };
        }

        return { taskStatus };
      };

      const payload = {
        ...rest,
        ...buildTaskStatus(),
      };

      return http.post(url, { ...payload, pageNo: pageParam, pageSize: 5, hasCount: false });
    },
    initialPageParam: 1,
    getNextPageParam: (last, all) => {
      const lastLength = last?.data?.length || 0;
      const hasNext = lastLength >= 5;

      if (hasNext) {
        return all.length + 1;
      }

      return;
    },
  });

  useEffect(() => {
    const emit = RevocationEmit.addListener('refresh', refetch);

    return () => {
      emit.remove();
    };
  }, [refetch]);

  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch]),
  );

  const list = useMemo(() => data?.pages.flatMap((item) => item.data), [data]);

  const handleLoadMore = useCallback(() => {
    if (hasNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage]);

  return (
    <ScreenTTIMeasure interactive={!isLoading}>
      <View className="flex-1">
        <Search
          timeInitialValue={[dayjs().subtract(30, 'day').toDate(), dayjs().toDate()]}
          value={{
            time: [dayjs(params?.startDate).toDate(), dayjs(params?.endDate).toDate()],
            groupId: params?.groupId,
            shopIds: params.shopIds?.length ? params.shopIds.join('') : undefined,
            custom: {
              taskStatus: params?.taskStatus,
              taskUserId: params?.taskUserId,
              taskSubType: params.taskSubType,
              alarmLevel: params?.alarmLevel,
              mustFlag: params?.mustFlag,
            },
          }}
          options={
            isSuperManger
              ? options.concat([
                  {
                    type: 'Button',
                    divider: true,
                    dataIndex: 'custom',
                    itemOptions: [
                      {
                        title: '巡检状态',
                        titleKey: 'taskStatus',
                        items: [
                          { label: '全部', value: undefined },
                          { label: '已提交', value: yytTaskStatusCNToEnEnum['审核中'] },
                          { label: '已完成', value: yytTaskStatusCNToEnEnum['已完成'] },
                        ],
                      },
                      {
                        title: '巡检类型',
                        titleKey: 'taskSubType',
                        items: [
                          { label: '全部', value: undefined },

                          {
                            label: '食安线下稽核',
                            value: CheckWayCNToEnEnum['食安线下稽核'],
                          },
                          {
                            label: '食安线上稽核',
                            value: CheckWayCNToEnEnum['食安线上稽核'],
                          },
                        ],
                      },
                    ],
                  },
                ])
              : options.concat([
                  {
                    type: 'Button',
                    divider: true,
                    dataIndex: 'custom',
                    itemOptions: [
                      {
                        title: '巡检状态',
                        titleKey: 'taskStatus',
                        items: [
                          { label: '全部', value: undefined },
                          { label: '已提交', value: yytTaskStatusCNToEnEnum['审核中'] },
                          { label: '待开始', value: yytTaskStatusCNToEnEnum['待开始'] },
                          { label: '进行中', value: yytTaskStatusCNToEnEnum['进行中'] },
                          { label: '已完成', value: yytTaskStatusCNToEnEnum['已完成'] },
                          { label: '已作废', value: yytTaskStatusCNToEnEnum['已作废'] },
                          { label: '已过期', value: yytTaskStatusCNToEnEnum['已逾期'] },
                          { label: '执行确认中', value: yytTaskStatusCNToEnEnum['执行确认中'] },
                          { label: '转办审核中', value: yytTaskStatusCNToEnEnum['转办审核中'] },
                          { label: '逾期进行中', value: TaskStatus.逾期进行中 },
                          // 后端无这个状态, 需要前端特殊处理
                          { label: '逾期已完成', value: -1 },
                        ],
                      },
                      {
                        title: '巡检类型',
                        titleKey: 'taskSubType',
                        items: [
                          { label: '全部', value: undefined },
                          {
                            label: '到店巡检',
                            value: CheckWayCNToEnEnum['到店巡检'],
                          },
                          {
                            label: '食安线下稽核',
                            value: CheckWayCNToEnEnum['食安线下稽核'],
                          },
                          {
                            label: '食安线上稽核',
                            value: CheckWayCNToEnEnum['食安线上稽核'],
                          },
                          {
                            label: '诊断巡检',
                            value: CheckWayCNToEnEnum['诊断巡检'],
                          },
                          {
                            label: '食安稽核到店辅导',
                            value: CheckWayCNToEnEnum['食安稽核到店辅导'],
                          },
                          {
                            label: '差异项到店任务',
                            value: CheckWayCNToEnEnum['差异项到店任务'],
                          },
                          {
                            label: '交接任务',
                            value: CheckWayCNToEnEnum['交接任务'],
                          },
                        ],
                      },
                      {
                        title: '诊断类型',
                        titleKey: 'alarmLevel',
                        items: [
                          { label: '全部', value: undefined },
                          {
                            label: '倔强青铜',
                            value: AlarmLevelEnum.橙色告警,
                          },
                          {
                            label: '秩序白银',
                            value: AlarmLevelEnum.红色告警,
                          },
                          {
                            label: '尊贵铂金',
                            value: AlarmLevelEnum.黄色告警,
                          },
                          {
                            label: '至尊星耀',
                            value: AlarmLevelEnum.绿色告警,
                          },
                          {
                            label: '传奇王者',
                            value: AlarmLevelEnum.白色告警,
                          },
                        ],
                      },
                      {
                        title: '是否必检',
                        titleKey: 'mustFlag',
                        items: [
                          { label: '全部', value: undefined },
                          { label: '必检', value: true },
                          { label: '非必检', value: false },
                        ],
                      },
                    ],
                  },
                ])
          }
          onSearchParamsChange={(val) => {
            onChange({
              endDate: !val?.time[1]
                ? formatDateToUTC(dayjs().endOf('day'))
                : formatDateToUTC(dayjs(val?.time[1]).endOf('day')),
              startDate: !val?.time[0]
                ? formatDateToUTC(dayjs().subtract(30, 'day').startOf('day'))
                : formatDateToUTC(dayjs(val?.time[0]).startOf('day')),
              shopIds: !val?.shopIds ? undefined : [val?.shopIds],
              groupId: val?.groupId,
              ...val.custom,
            });
          }}
        />
        <View className="flex-1">
          <FlashList
            estimatedItemSize={165}
            data={list}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.5}
            refreshControl={<RefreshControl refreshing={isRefetching || isLoading} onRefresh={refetch} />}
            renderItem={({ item }) => (
              <InspectionCard {...item} isSuperManger={isSuperManger} className="mx-3" onRefresh={refetch} />
            )}
            // eslint-disable-next-line react/no-unstable-nested-components
            ListFooterComponent={() => {
              if (!list || list.length === 0) {
                return null;
              }

              return (
                <View className="items-center p-4">
                  {isFetching ? (
                    <Loading>
                      <Text className="text-sm text-primary">加载中...</Text>
                    </Loading>
                  ) : !hasNextPage ? (
                    <Text className="text-[#5E5E5E]">没有更多数据了</Text>
                  ) : null}
                </View>
              );
            }}
            ListEmptyComponent={
              <View
                className="items-center justify-center"
                style={{
                  height: Dimensions.get('window').height - 120,
                }}
              >
                {isLoading ? <Loading /> : <Empty />}
              </View>
            }
          />
        </View>
      </View>
    </ScreenTTIMeasure>
  );
}

export default memo(InspectionPage);
