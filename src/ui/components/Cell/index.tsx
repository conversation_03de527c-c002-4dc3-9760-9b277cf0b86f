import { FunctionComponent } from 'react';
import { cn } from '@src/utils';
import { cva } from 'class-variance-authority';
import { Text, TouchableHighlight, View } from 'react-native';
import { CellProps } from './types';
import Icon from '../Icon';

const defaultProps = {
  title: null,
  subTitle: null,
  desc: null,
  descTextAlign: 'right',
  isLink: false,
  icon: '',
  roundRadius: 6,
  center: false,
  size: 'base',
  iconSlot: null,
  linkSlot: null,
  cellGroup: false,
  isLast: false,
} as CellProps;

const cellTextVariants = cva(['text-sm text-[#858585]'], {
  variants: {
    size: {
      base: 'text-base',
      small: 'text-sm',
      large: 'text-lg',
    },
  },
  defaultVariants: {
    size: 'base',
  },
});

const containerVariants = cva(['relative w-full bg-white'], {
  variants: {
    size: {
      base: 'px-4 py-3',
      small: 'py-2',
      large: 'px-5 py-4',
    },
  },
  defaultVariants: {
    size: 'base',
  },
});

export const Cell: FunctionComponent<Partial<CellProps>> = (props) => {
  const {
    children,
    onClick,
    title,
    titleClassName,
    titleWrapperClassName,
    subTitle,
    desc,
    descClassName,
    descTextAlign,
    isLink,
    icon,
    roundRadius,
    center,
    size,
    iconSlot,
    linkSlot,
    cellGroup,
    isLast,
    className,
    componentClassName,
    cellContainerClassName,
  } = {
    ...defaultProps,
    ...props,
  };

  const handleClick = (event: any) => {
    onClick && onClick(event);
  };

  const renderComponent = () => {
    if (children) {
      return children;
    }

    return (
      <View className={cn('flex-row w-full justify-between', componentClassName)}>
        {iconSlot || null}
        {icon ? <Icon name={icon} className="mr-1 size-4.5" /> : null}
        {title || subTitle ? (
          <View className={cn('mr-2 min-w-21 flex-col justify-between', titleWrapperClassName)}>
            {title ? (
              <Text className={cn('text-base', cellTextVariants({ size }), titleClassName)}>{title}</Text>
            ) : null}
            {subTitle ? <Text className={cn(cellTextVariants({ size }), 'text-xs')}>{subTitle}</Text> : null}
          </View>
        ) : null}
        {typeof desc === 'string' || typeof desc === 'number' ? (
          <View
            className={cn('self-start text-right flex-1', {
              'text-center': center,
              'text-right': descTextAlign === 'right',
              'text-left': descTextAlign === 'left',
            })}
          >
            <Text
              className={cn(
                'text-sm',
                {
                  'text-[#666666]': !title && !subTitle,
                },
                cellTextVariants({ size }),
                'text-dark',
                descClassName,
              )}
            >
              {desc}
            </Text>
          </View>
        ) : (
          desc
        )}
        {!linkSlot && isLink ? <Icon name="chevron-small-right" /> : linkSlot}
      </View>
    );
  };

  if (isLink) {
    return (
      <TouchableHighlight
        className={cn('w-full')}
        activeOpacity={1}
        onPress={handleClick}
        style={{
          borderRadius: roundRadius,
        }}
      >
        <View
          className={cn(
            {
              'm-0 border-b-hairline border-b-[#f5f6f7] ': cellGroup,
              'm-0 border-none border-0': isLast,
            },
            containerVariants({ size }),
            cellContainerClassName,
          )}
          style={{
            borderRadius: roundRadius,
          }}
        >
          {renderComponent()}
        </View>
      </TouchableHighlight>
    );
  }

  if (onClick) {
    return (
      <TouchableHighlight
        className={cn('w-full rounded-md')}
        activeOpacity={1}
        onPress={onClick}
        style={{
          borderRadius: roundRadius,
        }}
      >
        <View
          className={cn(
            {
              'm-0 border-b-hairline border-b-[#f5f6f7] ': cellGroup,
              'm-0 border-none border-0': isLast,
            },
            containerVariants({ size }),
            cellContainerClassName,
          )}
          style={{
            borderRadius: roundRadius,
          }}
        >
          {renderComponent()}
        </View>
      </TouchableHighlight>
    );
  }

  return (
    <View
      className={cn(
        {
          'm-0 border-b-hairline border-b-[#f5f6f7] ': cellGroup,
          'm-0 border-none border-0': isLast,
        },
        containerVariants({ size }),
        cellContainerClassName,
        className,
      )}
      style={{
        borderRadius: roundRadius,
      }}
    >
      {renderComponent()}
    </View>
  );
};
