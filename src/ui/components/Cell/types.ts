import { ReactNode } from 'react';

export interface CellProps {
  title: ReactNode; // 标题名称
  subTitle: ReactNode; // 左侧副标题
  desc: ReactNode; // 右侧描述
  titleWrapperClassName?: string; // 自定义 title 父级 样式
  titleClassName?: string; // 自定义 title 样式
  descClassName?: string; // 右侧类名
  descTextAlign: string; // 右侧描述文本对齐方式 text-align
  isLink: boolean; // 是否展示右侧箭头并开启点击反馈 默认FALSE
  icon: string; // 左侧 图标名称 或图片链接
  roundRadius: number; // 圆角半径
  url: string; // 点击后跳转的链接地址
  to: string; // 点击后跳转的目标路由对象，同 vue-router 的 to 属性 属性
  replace: boolean; // 是否在跳转时替换当前页面历史 默认 FALSE
  center: boolean; // 是否使内容垂直居中 默认FALSE
  size: 'small' | 'base' | 'large'; // 单元格大小，可选值为 large
  iconSlot: ReactNode;
  linkSlot: ReactNode;
  cellGroup: boolean;
  isLast: boolean;
  className?: string;
  componentClassName?: string;
  click: (event: any) => void;
  onClick: (event: any) => void;
  children: ReactNode;
  cellContainerClassName?: string;
}
